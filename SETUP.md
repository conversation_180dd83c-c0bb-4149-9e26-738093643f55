# Setup Guide

This guide will help you set up the YouTube Subtitle Extractor with authentication and subscription features.

## 1. Supabase Setup

### Create a Supabase Project
1. Go to [supabase.com](https://supabase.com) and create an account
2. Create a new project
3. Wait for the project to be ready

### Configure Authentication
1. Go to Authentication > Settings in your Supabase dashboard
2. Add your site URL (e.g., `http://localhost:5173` for development)
3. Configure Google OAuth:
   - Go to Authentication > Providers
   - Enable Google provider
   - Add your Google OAuth credentials (see Google Setup below)

### Set up Database
1. Go to SQL Editor in your Supabase dashboard
2. Copy and paste the contents of `database-setup.sql`
3. Run the SQL to create tables and policies

### Get Your Keys
1. Go to Settings > API
2. Copy your Project URL and anon public key
3. Copy your service role secret key (keep this secure!)

## 2. Google OAuth Setup

### Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select existing one
3. Enable the Google+ API

### Configure OAuth Consent Screen
1. Go to APIs & Services > OAuth consent screen
2. Choose "External" user type
3. Fill in required information:
   - App name: "YouTube Subtitle Extractor"
   - User support email: your email
   - Developer contact: your email

### Create OAuth Credentials
1. Go to APIs & Services > Credentials
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "Web application"
4. Add authorized redirect URIs:
   - `https://your-project.supabase.co/auth/v1/callback`
   - `http://localhost:5173/auth/callback` (for development)
5. Copy Client ID and Client Secret

### Configure in Supabase
1. Go back to Supabase > Authentication > Providers > Google
2. Enable Google provider
3. Add your Client ID and Client Secret

## 3. Stripe Setup

### Create Stripe Account
1. Go to [stripe.com](https://stripe.com) and create an account
2. Complete account verification

### Create Products and Prices
1. Go to Products in your Stripe dashboard
2. Create three products:

**Starter Plan**
- Name: "Starter"
- Price: $9.00 USD
- Billing: Monthly recurring
- Copy the Price ID (starts with `price_`)

**Pro Plan**
- Name: "Pro" 
- Price: $19.00 USD
- Billing: Monthly recurring
- Copy the Price ID

**Premium Plan**
- Name: "Premium"
- Price: $39.00 USD
- Billing: Monthly recurring
- Copy the Price ID

### Set up Webhooks
1. Go to Developers > Webhooks
2. Add endpoint: `https://your-domain.com/api/stripe/webhook`
3. Select events:
   - `checkout.session.completed`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
4. Copy the webhook signing secret

### Get Your Keys
1. Go to Developers > API keys
2. Copy your Publishable key and Secret key
3. For production, use live keys instead of test keys

## 4. Environment Variables

Create a `.env` file in your project root:

```bash
# Proxy Configuration
SOCKET_URL='your_proxy_url_here'

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SUPABASE_PROJECT_ID=your_supabase_project_id

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Stripe Price IDs
NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID=price_...
NEXT_PUBLIC_STRIPE_PRO_PRICE_ID=price_...
NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID=price_...
```

## 5. Testing

### Test Authentication
1. Start your development server: `npm run dev`
2. Try signing in with Google
3. Check that user data appears in Supabase

### Test Payments (Test Mode)
1. Use Stripe test card numbers:
   - Success: `4242 4242 4242 4242`
   - Decline: `4000 0000 0000 0002`
2. Test subscription creation and cancellation
3. Verify webhook events are received

### Test Subtitle Extraction
1. Sign in and subscribe to a plan
2. Try extracting subtitles from a YouTube video
3. Verify usage tracking works

## 6. Deployment

### Vercel Deployment
1. Install Vercel CLI: `npm install -g vercel`
2. Login: `vercel login`
3. Deploy: `vercel --prod`
4. Add environment variables in Vercel dashboard
5. Update webhook URLs to production domain

### Update Configurations
1. Update Google OAuth redirect URIs with production URL
2. Update Stripe webhook endpoint with production URL
3. Update Supabase site URL with production URL

## Troubleshooting

### Common Issues
1. **OAuth not working**: Check redirect URIs match exactly
2. **Webhooks failing**: Verify endpoint URL and signing secret
3. **Database errors**: Check RLS policies and user permissions
4. **Payment issues**: Verify Stripe keys and webhook configuration

### Support
- Check browser console for errors
- Review Supabase logs
- Check Stripe webhook logs
- Verify environment variables are set correctly
