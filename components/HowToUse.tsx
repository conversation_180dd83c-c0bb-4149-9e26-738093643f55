import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Link, 
  Search, 
  Download, 
  Eye, 
  CheckCircle,
  ArrowRight
} from 'lucide-react';

const HowToUse = () => {
  const steps = [
    {
      step: 1,
      title: "Paste YouTube URL",
      description: "Copy and paste any YouTube video URL into the input field. Our system will automatically validate the URL and detect if it's a valid YouTube video.",
      icon: <Link className="w-8 h-8" />,
      image: "/images/step1-url-input.png", // We'll create placeholder images
      tips: [
        "Works with any YouTube video URL format",
        "Automatic URL validation with instant feedback",
        "Supports both youtube.com and youtu.be links"
      ]
    },
    {
      step: 2,
      title: "Select Language",
      description: "Browse through available subtitle languages with our searchable interface. English is automatically selected when available.",
      icon: <Search className="w-8 h-8" />,
      image: "/images/step2-language-select.png",
      tips: [
        "Search through 70+ supported languages",
        "Auto-generated subtitles clearly marked",
        "English automatically selected as default"
      ]
    },
    {
      step: 3,
      title: "Extract & Process",
      description: "Watch the real-time progress as we extract and clean the subtitles. Our advanced processing ensures perfect formatting.",
      icon: <Download className="w-8 h-8" />,
      image: "/images/step3-extraction.png",
      tips: [
        "Real-time progress tracking",
        "Advanced subtitle cleaning algorithms",
        "Automatic formatting optimization"
      ]
    },
    {
      step: 4,
      title: "Preview & Download",
      description: "Preview your subtitles in VTT, SRT, or TXT format before downloading. See exactly what you'll get with full content preview.",
      icon: <Eye className="w-8 h-8" />,
      image: "/images/step4-preview-download.png",
      tips: [
        "Preview in multiple formats",
        "Full content scrollable preview",
        "One-click download in preferred format"
      ]
    }
  ];

  return (
    <section id="how-to-use" className="py-12 sm:py-20 bg-slate-800/30">
      <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12 sm:mb-16"
        >
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4">
            How to Use
          </h2>
          <p className="text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2">
            Extract YouTube subtitles in 4 simple steps. No registration required, completely free to use.
          </p>
        </motion.div>

        <div className="space-y-12 sm:space-y-16">
          {steps.map((stepData, index) => (
            <motion.div
              key={stepData.step}
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-8 sm:gap-12`}
            >
              {/* Content Side */}
              <div className="flex-1 space-y-4 sm:space-y-6">
                <div className="flex items-center space-x-3 sm:space-x-4">
                  <div className="flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full">
                    <span className="text-lg sm:text-2xl font-bold text-white">{stepData.step}</span>
                  </div>
                  <div className="text-purple-400">
                    {stepData.icon}
                  </div>
                </div>

                <div>
                  <h3 className="text-2xl sm:text-3xl font-bold text-white mb-3 sm:mb-4">
                    {stepData.title}
                  </h3>
                  <p className="text-base sm:text-lg text-gray-300 leading-relaxed">
                    {stepData.description}
                  </p>
                </div>

                <div className="space-y-2 sm:space-y-3">
                  <h4 className="text-base sm:text-lg font-semibold text-white">Key Features:</h4>
                  <ul className="space-y-2">
                    {stepData.tips.map((tip, tipIndex) => (
                      <li key={tipIndex} className="flex items-start space-x-2 sm:space-x-3">
                        <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-400 flex-shrink-0 mt-0.5" />
                        <span className="text-sm sm:text-base text-gray-300">{tip}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {index < steps.length - 1 && (
                  <div className="flex items-center space-x-2 text-purple-400">
                    <span className="text-xs sm:text-sm font-medium">Next Step</span>
                    <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4" />
                  </div>
                )}
              </div>

              {/* Image Side */}
              <div className="flex-1 w-full">
                <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700 overflow-hidden">
                  <CardContent className="p-0">
                    <div className="aspect-video bg-gradient-to-br from-slate-700 to-slate-800 flex items-center justify-center">
                      {/* Placeholder for actual screenshots */}
                      <div className="text-center p-4">
                        <div className="w-16 h-16 sm:w-24 sm:h-24 mx-auto mb-3 sm:mb-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
                          {stepData.icon}
                        </div>
                        <h4 className="text-white font-semibold mb-2 text-sm sm:text-base">Step {stepData.step} Preview</h4>
                        <p className="text-gray-400 text-xs sm:text-sm">Screenshot coming soon</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        {/* <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-2xl p-8 border border-purple-500/30">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Extract Subtitles?
            </h3>
            <p className="text-gray-300 mb-6">
              Follow these simple steps and get professional-quality subtitles in seconds
            </p>
            <div className="flex items-center justify-center space-x-2 text-purple-400">
              <span className="text-sm">Scroll up to get started</span>
              <ArrowRight className="w-4 h-4 rotate-[-90deg]" />
            </div>
          </div>
        </motion.div> */}
      </div>
    </section>
  );
};

export default HowToUse;
