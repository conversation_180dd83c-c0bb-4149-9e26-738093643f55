import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { PRICING_TIERS, type PricingTier } from '@/lib/stripe';
import type { Subscription, UsageStats } from '@/lib/types/subscription';
import { useAuth } from '@/components/contexts/AuthContext';
import toast from 'react-hot-toast';

export const useSubscription = () => {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [usage, setUsage] = useState<UsageStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchSubscriptionData();
    } else {
      setSubscription(null);
      setUsage(null);
      setLoading(false);
    }
  }, [user]);

  const fetchSubscriptionData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch subscription
      const { data: subscriptionData, error: subscriptionError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (subscriptionError && subscriptionError.code !== 'PGRST116') {
        throw subscriptionError;
      }

      // Fetch usage stats
      const { data: usageData, error: usageError } = await supabase
        .from('usage_stats')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (usageError && usageError.code !== 'PGRST116') {
        throw usageError;
      }

      setSubscription(subscriptionData as Subscription);
      setUsage(usageData as UsageStats);
    } catch (err) {
      console.error('Error fetching subscription data:', err);
      setError('Failed to fetch subscription data');
    } finally {
      setLoading(false);
    }
  };

  const canPerformAction = (creditsNeeded: number = 1): boolean => {
    if (!subscription || subscription.status !== 'active') {
      return false;
    }

    const tier = PRICING_TIERS[subscription.tier as PricingTier];
    if (!tier) return false;

    // Check if user has enough credits
    const currentCreditsUsed = usage?.credits_used_this_month || 0;
    const availableCredits = tier.limits.creditsPerMonth - currentCreditsUsed;

    return availableCredits >= creditsNeeded;
  };

  // Backward compatibility - check if user can extract a complete video (2 credits)
  const canExtractVideo = (): boolean => {
    return canPerformAction(2); // Get languages (1) + Download (1) = 2 credits
  };

  const getRemainingCredits = (): number => {
    if (!subscription || subscription.status !== 'active') {
      return 0;
    }

    const tier = PRICING_TIERS[subscription.tier as PricingTier];
    if (!tier) return 0;

    const currentCreditsUsed = usage?.credits_used_this_month || 0;
    return Math.max(0, tier.limits.creditsPerMonth - currentCreditsUsed);
  };

  // Backward compatibility - get remaining complete extractions
  const getRemainingExtractions = (): number => {
    const remainingCredits = getRemainingCredits();
    return Math.floor(remainingCredits / 2); // Each complete extraction needs 2 credits
  };

  const createCheckoutSession = async (tier: PricingTier): Promise<string> => {
    if (!user) {
      throw new Error('User must be authenticated');
    }

    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId: PRICING_TIERS[tier].priceId,
          userId: user.id,
          userEmail: user.email
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create checkout session');
      }

      const { sessionId } = await response.json();
      return sessionId;
    } catch (err) {
      console.error('Error creating checkout session:', err);
      toast.error('Failed to create checkout session');
      throw err;
    }
  };

  const cancelSubscription = async (): Promise<void> => {
    if (!subscription) {
      throw new Error('No active subscription found');
    }

    try {
      const response = await fetch('/api/user/subscription', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId: subscription.stripe_subscription_id
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to cancel subscription');
      }

      toast.success('Subscription cancelled successfully');
      await fetchSubscriptionData();
    } catch (err) {
      console.error('Error cancelling subscription:', err);
      toast.error('Failed to cancel subscription');
      throw err;
    }
  };

  const refreshSubscription = async (): Promise<void> => {
    await fetchSubscriptionData();
  };

  return {
    subscription,
    usage,
    loading,
    error,
    canPerformAction,
    canExtractVideo,
    getRemainingCredits,
    getRemainingExtractions,
    createCheckoutSession,
    cancelSubscription,
    refreshSubscription
  };
};
