import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollA<PERSON> } from "@/components/ui/scroll-area";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Clock, Trash2 } from "lucide-react";

interface VideoHistoryItem {
  id: string;
  title: string;
  url: string;
  thumbnailUrl: string;
  extractedAt: Date;
  language?: string;
}

interface VideoHistoryProps {
  historyItems?: VideoHistoryItem[];
  onSelectVideo?: (videoItem: VideoHistoryItem) => void;
  onClearHistory?: () => void;
  onRemoveHistoryItem?: (id: string) => void;
}

const VideoHistory: React.FC<VideoHistoryProps> = ({
  historyItems = [
    {
      id: "1",
      title: "Introduction to React",
      url: "https://www.youtube.com/watch?v=example1",
      thumbnailUrl:
        "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&q=80",
      extractedAt: new Date(),
      language: "English",
    },
    {
      id: "2",
      title: "Advanced TypeScript Tutorial",
      url: "https://www.youtube.com/watch?v=example2",
      thumbnailUrl:
        "https://images.unsplash.com/photo-1587620962725-abab7fe55159?w=300&q=80",
      extractedAt: new Date(Date.now() - 86400000), // 1 day ago
      language: "English",
    },
    {
      id: "3",
      title: "Building a Full Stack Application",
      url: "https://www.youtube.com/watch?v=example3",
      thumbnailUrl:
        "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=300&q=80",
      extractedAt: new Date(Date.now() - 172800000), // 2 days ago
      language: "Spanish",
    },
  ],
  onSelectVideo = () => {},
  onClearHistory = () => {},
  onRemoveHistoryItem = () => {},
}) => {
  const [hoveredItemId, setHoveredItemId] = useState<string | null>(null);

  const formatDate = (date: Date): string => {
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60),
    );

    if (diffInHours < 24) {
      return diffInHours === 0
        ? "Just now"
        : `${diffInHours} hour${diffInHours > 1 ? "s" : ""} ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} day${diffInDays > 1 ? "s" : ""} ago`;
    }
  };

  if (historyItems.length === 0) {
    return (
      <div className="bg-background p-4 rounded-lg border border-border">
        <div className="flex items-center justify-center h-32 text-muted-foreground">
          <div className="text-center">
            <Clock className="mx-auto h-8 w-8 mb-2" />
            <p>No video history yet</p>
            <p className="text-sm">
              Extract subtitles from a video to see it here
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background p-4 rounded-lg border border-border">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Recently Extracted Videos</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={onClearHistory}
          className="text-muted-foreground"
        >
          Clear History
        </Button>
      </div>

      <ScrollArea className="w-full">
        <div className="flex space-x-4 pb-4">
          {historyItems.map((item) => (
            <TooltipProvider key={item.id}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Card
                    className="min-w-[200px] max-w-[200px] cursor-pointer relative"
                    onMouseEnter={() => setHoveredItemId(item.id)}
                    onMouseLeave={() => setHoveredItemId(null)}
                    onClick={() => onSelectVideo(item)}
                  >
                    <div className="relative">
                      <img
                        src={item.thumbnailUrl}
                        alt={item.title}
                        className="w-full h-[112px] object-cover rounded-t-lg"
                      />
                      {hoveredItemId === item.id && (
                        <Button
                          variant="destructive"
                          size="icon"
                          className="absolute top-1 right-1 h-6 w-6"
                          onClick={(e) => {
                            e.stopPropagation();
                            onRemoveHistoryItem(item.id);
                          }}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                    <CardContent className="p-3">
                      <p
                        className="font-medium text-sm line-clamp-2"
                        title={item.title}
                      >
                        {item.title}
                      </p>
                      <div className="flex justify-between items-center mt-2 text-xs text-muted-foreground">
                        <span>{item.language || "Unknown"}</span>
                        <span>{formatDate(item.extractedAt)}</span>
                      </div>
                    </CardContent>
                  </Card>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{item.title}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};

export default VideoHistory;
