import { GitHubLogoIcon, TwitterLogoIcon } from '@radix-ui/react-icons';
import { motion } from 'framer-motion';
import { Mail, Heart, Shield, FileText, HelpCircle } from 'lucide-react';

interface FooterProps {
  onTermsClick?: () => void;
  onPrivacyClick?: () => void;
  onDisclaimerClick?: () => void;
}

const Footer = ({ onTermsClick, onPrivacyClick, onDisclaimerClick }: FooterProps) => {
  const currentYear = new Date().getFullYear();

  const links = {
    product: [
      { name: 'Features', href: '#features' },
      { name: 'How to Use', href: '#how-to-use' },
      { name: 'Use Cases', href: '#use-cases' },
      { name: 'FAQ', href: '#faq' }
    ],
    legal: [
      { name: 'Privacy Policy', onClick: onPrivacyClick },
      { name: 'Terms of Service', onClick: onTermsClick },
      { name: 'Disclaimer', onClick: onDisclaimerClick }
    ],
    social: [
      { name: 'GitHub', href: '#', icon: <GitHubLogoIcon className="w-5 h-5" /> },
      { name: 'Twitter', href: '#', icon: <TwitterLogoIcon className="w-5 h-5" /> },
      { name: 'Email', href: 'mailto:<EMAIL>', icon: <Mail className="w-5 h-5" /> }
    ]
  };

  return (
    <footer className="bg-slate-900 border-t border-slate-800">
      <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-8 sm:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
          {/* Brand Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="col-span-1 sm:col-span-2"
          >
            <h3 className="text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4">
              YouTube Subtitle
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                {" "}Extractor
              </span>
            </h3>
            <p className="text-gray-400 mb-4 sm:mb-6 max-w-md text-sm sm:text-base">
              The most advanced YouTube subtitle extraction tool. Extract, clean, and download
              subtitles from videos and playlists with professional-grade accuracy.
            </p>
            <div className="flex space-x-3 sm:space-x-4">
              {links.social.map((social) => (
                <motion.a
                  key={social.name}
                  href={social.href}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  className="text-gray-400 hover:text-purple-400 transition-colors duration-200"
                  aria-label={social.name}
                >
                  {social.icon}
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Product Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <h4 className="text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4">Product</h4>
            <ul className="space-y-1 sm:space-y-2">
              {links.product.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Legal Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h4 className="text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4">Legal</h4>
            <ul className="space-y-1 sm:space-y-2">
              {links.legal.map((link) => (
                <li key={link.name}>
                  {link.onClick ? (
                    <button
                      onClick={link.onClick}
                      className="text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base"
                    >
                      {link.name}
                    </button>
                  ) : (
                    <a
                      // href={link.href}
                      className="text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base"
                    >
                      {link.name}
                    </a>
                  )}
                </li>
              ))}
            </ul>
          </motion.div>
        </div>

        {/* Bottom Bar */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="flex flex-col lg:flex-row justify-between items-center pt-6 sm:pt-8 mt-6 sm:mt-8 border-t border-slate-800 space-y-4 lg:space-y-0"
        >
          <div className="flex items-center text-gray-400 text-xs sm:text-sm text-center lg:text-left">
            <span>© {currentYear} DownloadYTSubtitles.com. Made with</span>
            <Heart className="w-3 h-3 sm:w-4 sm:h-4 text-red-400 mx-1" />
            <span>for the community.</span>
          </div>

          <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 lg:space-x-6 text-xs sm:text-sm text-gray-400">
            <div className="flex items-center space-x-1">
              <Shield className="w-3 h-3 sm:w-4 sm:h-4" />
              <span>Privacy First</span>
            </div>
            <div className="flex items-center space-x-1">
              <HelpCircle className="w-3 h-3 sm:w-4 sm:h-4" />
              <span>Free During Beta</span>
            </div>
            <div className="flex items-center space-x-1">
              <FileText className="w-3 h-3 sm:w-4 sm:h-4" />
              <span>Professional Quality</span>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
