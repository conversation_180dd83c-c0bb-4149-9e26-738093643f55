import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Shield, Eye, Database, Lock, Globe } from 'lucide-react';

interface PrivacyPolicyProps {
  onBack: () => void;
}

const PrivacyPolicy = ({ onBack }: PrivacyPolicyProps) => {
  const sections = [
    {
      title: "Information We Collect",
      icon: <Database className="w-5 h-5" />,
      content: [
        "We do NOT collect any personal information from users.",
        "We do NOT require registration, login, or any form of account creation.",
        "We do NOT store YouTube URLs, video content, or extracted subtitle data.",
        "We do NOT use cookies for tracking or analytics purposes.",
        "The only data processed is the YouTube URL you provide, which is used temporarily to extract subtitles and then immediately discarded."
      ]
    },
    {
      title: "How We Process Data",
      icon: <Lock className="w-5 h-5" />,
      content: [
        "When you submit a YouTube URL, our server temporarily processes it to extract publicly available subtitle data.",
        "All processing happens in real-time with no data retention.",
        "Subtitle extraction uses YouTube's public API endpoints that are already accessible.",
        "No video content is downloaded, stored, or cached on our servers.",
        "All temporary data is immediately purged after processing is complete."
      ]
    },
    {
      title: "Data Storage and Retention",
      icon: <Shield className="w-5 h-5" />,
      content: [
        "We do NOT store any user data, URLs, or extracted content.",
        "No databases are used to retain user information.",
        "All processing is stateless and temporary.",
        "Server logs may contain basic technical information (IP addresses, timestamps) for security purposes only.",
        "Any technical logs are automatically purged within 24 hours."
      ]
    },
    {
      title: "Third-Party Services",
      icon: <Globe className="w-5 h-5" />,
      content: [
        "We interact with YouTube's public APIs to extract subtitle data.",
        "We do not share any data with third-party analytics or tracking services.",
        "No external advertising networks or data brokers are used.",
        "The service operates independently without external data sharing."
      ]
    },
    {
      title: "Your Privacy Rights",
      icon: <Eye className="w-5 h-5" />,
      content: [
        "Since we don't collect personal data, there's no personal data to access, modify, or delete.",
        "You can use our service completely anonymously.",
        "No account creation means no data tied to your identity.",
        "You have complete control over what URLs you submit for processing."
      ]
    },
    {
      title: "Security Measures",
      icon: <Shield className="w-5 h-5" />,
      content: [
        "All connections to our service use HTTPS encryption.",
        "Server infrastructure follows industry-standard security practices.",
        "No sensitive data is stored, reducing security risks.",
        "Regular security updates and monitoring are maintained."
      ]
    },
    {
      title: "Children's Privacy",
      icon: <Shield className="w-5 h-5" />,
      content: [
        "Our service does not knowingly collect information from children under 13.",
        "Since no personal information is collected from any users, this includes children.",
        "Parents can safely allow children to use this service as no data collection occurs."
      ]
    },
    {
      title: "International Users",
      icon: <Globe className="w-5 h-5" />,
      content: [
        "This service can be used globally without data residency concerns.",
        "Since no personal data is collected, GDPR and similar regulations are not applicable.",
        "Users from all countries can use the service with the same privacy protections."
      ]
    },
    {
      title: "Changes to Privacy Policy",
      icon: <Eye className="w-5 h-5" />,
      content: [
        "Any changes to this privacy policy will be posted on this page.",
        "We will update the 'Last Updated' date when changes are made.",
        "Continued use of the service constitutes acceptance of any changes.",
        "We are committed to maintaining our no-data-collection approach."
      ]
    },
    {
      title: "Contact Us",
      icon: <Shield className="w-5 h-5" />,
      content: [
        "If you have questions about this Privacy Policy, please contact us through our support channels.",
        "We are committed to transparency about our privacy practices.",
        "We will respond to legitimate privacy inquiries promptly."
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <Button
            variant="ghost"
            onClick={onBack}
            className="text-white hover:text-purple-400"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
          
          <div className="text-right">
            <p className="text-gray-400 text-sm">Last updated: {new Date().toLocaleDateString()}</p>
          </div>
        </motion.div>

        {/* Title */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Privacy Policy
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Your privacy is important to us. This policy explains our privacy practices for YouTube Subtitle Extractor.
          </p>
        </motion.div>

        {/* Privacy Notice Banner */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <Card className="bg-green-900/20 border-green-500/30">
            <CardContent className="p-6">
              <div className="flex items-start space-x-4">
                <Shield className="w-8 h-8 text-green-400 flex-shrink-0 mt-1" />
                <div>
                  <h3 className="text-lg font-semibold text-green-400 mb-2">Privacy-First Approach</h3>
                  <p className="text-gray-300">
                    <strong>We collect ZERO personal data.</strong> No registration, no tracking, no data storage. 
                    Your privacy is completely protected because we simply don't collect any information about you.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Privacy Sections */}
        <div className="space-y-6">
          {sections.map((section, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * (index + 3) }}
            >
              <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center space-x-3">
                    <div className="text-purple-400">
                      {section.icon}
                    </div>
                    <span>{section.title}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {section.content.map((paragraph, pIndex) => (
                      <p key={pIndex} className="text-gray-300 leading-relaxed">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-12 text-center"
        >
          <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
            <CardContent className="p-6">
              <p className="text-gray-300 mb-4">
                This Privacy Policy demonstrates our commitment to protecting your privacy through a 
                no-data-collection approach. You can use our service with complete confidence.
              </p>
              <p className="text-sm text-gray-400">
                This policy is effective as of {new Date().toLocaleDateString()} and will remain in effect 
                except with respect to any changes in its provisions in the future.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default PrivacyPolicy;
