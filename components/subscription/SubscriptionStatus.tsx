import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '../ui/alert';
import { useSubscription } from '@/components/hooks/useSubscription';
import { PRICING_TIERS } from '@/lib/stripe';
import { Crown, Calendar, TrendingUp, AlertTriangle, Wifi, DollarSign, Info } from 'lucide-react';
import { format } from 'date-fns';

const SubscriptionStatus: React.FC = () => {
  const {
    subscription,
    usage,
    loading,
    getBandwidthInfo,
    shouldWarnAboutCredits,
    getUsageSuggestions
  } = useSubscription();

  if (loading) {
    return (
      <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-slate-700 rounded w-1/3"></div>
            <div className="h-8 bg-slate-700 rounded w-1/2"></div>
            <div className="h-4 bg-slate-700 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!subscription) {
    return (
      <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-yellow-500" />
            No Active Subscription
          </CardTitle>
          <CardDescription className="text-gray-300">
            Subscribe to a plan to start extracting YouTube subtitles
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const tier = PRICING_TIERS[subscription.tier as keyof typeof PRICING_TIERS];
  const currentCreditsUsed = usage?.credits_used_this_month || 0;
  const creditsLimit = tier?.limits.creditsPerMonth || 0;
  const usagePercentage = creditsLimit === 0 ? 0 : (currentCreditsUsed / creditsLimit) * 100;
  const remainingCredits = Math.max(0, creditsLimit - currentCreditsUsed);
  const remainingExtractions = Math.floor(remainingCredits / 2);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-600';
      case 'trialing':
        return 'bg-blue-600';
      case 'past_due':
        return 'bg-yellow-600';
      case 'canceled':
        return 'bg-red-600';
      default:
        return 'bg-gray-600';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'trialing':
        return 'Trial';
      case 'past_due':
        return 'Past Due';
      case 'canceled':
        return 'Canceled';
      default:
        return status;
    }
  };

  return (
    <div className="space-y-4">
      {/* Subscription Overview */}
      <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Crown className="w-5 h-5 text-yellow-500" />
            {tier?.name} Plan
          </CardTitle>
          <CardDescription className="text-gray-300 flex items-center gap-2">
            <Badge className={getStatusColor(subscription.status)}>
              {getStatusText(subscription.status)}
            </Badge>
            <span>${tier?.price}/month</span>
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="flex items-center gap-2 text-sm text-gray-400 mb-1">
                <Calendar className="w-4 h-4" />
                Current Period
              </div>
              <div className="text-white">
                {format(new Date(subscription.current_period_start), 'MMM d')} - {format(new Date(subscription.current_period_end), 'MMM d, yyyy')}
              </div>
            </div>
            <div>
              <div className="flex items-center gap-2 text-sm text-gray-400 mb-1">
                <TrendingUp className="w-4 h-4" />
                Next Billing
              </div>
              <div className="text-white">
                {subscription.cancel_at_period_end 
                  ? 'Cancels at period end' 
                  : format(new Date(subscription.current_period_end), 'MMM d, yyyy')
                }
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Statistics */}
      <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">Usage This Month</CardTitle>
          <CardDescription className="text-gray-300">
            Track your credit usage and remaining extractions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Credits Usage */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-400">Credits Used</span>
              <span className="text-white font-medium">
                {currentCreditsUsed} / {creditsLimit}
              </span>
            </div>
            <Progress
              value={usagePercentage}
              className="h-2"
            />
            <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
              <span>{remainingCredits} credits remaining</span>
              <span>~{remainingExtractions} complete extractions left</span>
            </div>
          </div>

          {/* Credit Breakdown */}
          <div className="bg-slate-700/50 rounded-lg p-3">
            <h4 className="text-sm font-medium text-white mb-2">High-Bandwidth Proxy Model</h4>
            <div className="space-y-1 text-xs text-gray-400">
              <div className="flex justify-between">
                <span>Get available languages:</span>
                <span>1 credit (~20MB)</span>
              </div>
              <div className="flex justify-between">
                <span>Download subtitles:</span>
                <span>1 credit (~20MB)</span>
              </div>
              <div className="flex justify-between border-t border-slate-600 pt-1 mt-1 font-medium text-gray-300">
                <span>Complete extraction:</span>
                <span>2 credits (~40MB)</span>
              </div>
            </div>
          </div>

          {/* Bandwidth Cost Information */}
          {getBandwidthInfo() && (
            <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-3">
              <h4 className="text-sm font-medium text-blue-300 mb-2 flex items-center gap-2">
                <Wifi className="w-4 h-4" />
                Bandwidth & Cost Info
              </h4>
              <div className="space-y-1 text-xs text-gray-400">
                <div className="flex justify-between">
                  <span>Bandwidth per credit:</span>
                  <span className="text-blue-300">{getBandwidthInfo()?.estimatedBandwidthPerAction}</span>
                </div>
                <div className="flex justify-between">
                  <span>Proxy cost per credit:</span>
                  <span className="text-blue-300">{getBandwidthInfo()?.estimatedCostPerAction}</span>
                </div>
                <div className="flex justify-between border-t border-blue-700/30 pt-1 mt-1 font-medium text-blue-200">
                  <span>Total monthly bandwidth:</span>
                  <span>~{(getBandwidthInfo()?.bandwidthPerCredit || 0) * creditsLimit}MB</span>
                </div>
              </div>
            </div>
          )}

          {/* Smart Usage Suggestions */}
          {getUsageSuggestions().length > 0 && (
            <div className="space-y-2">
              {getUsageSuggestions().map((suggestion, index) => (
                <Alert
                  key={index}
                  className={`border ${
                    suggestion.type === 'error'
                      ? 'border-red-700/30 bg-red-900/20'
                      : suggestion.type === 'warning'
                      ? 'border-yellow-700/30 bg-yellow-900/20'
                      : 'border-blue-700/30 bg-blue-900/20'
                  }`}
                >
                  {suggestion.type === 'error' ? (
                    <AlertTriangle className="h-4 w-4 text-red-400" />
                  ) : suggestion.type === 'warning' ? (
                    <AlertTriangle className="h-4 w-4 text-yellow-400" />
                  ) : (
                    <Info className="h-4 w-4 text-blue-400" />
                  )}
                  <AlertDescription className={`text-sm ${
                    suggestion.type === 'error'
                      ? 'text-red-300'
                      : suggestion.type === 'warning'
                      ? 'text-yellow-300'
                      : 'text-blue-300'
                  }`}>
                    {suggestion.message}
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          )}

          {tier && (
            <div className="pt-4 border-t border-slate-700">
              <h4 className="text-sm font-medium text-white mb-2">Plan Features</h4>
              <ul className="space-y-1 text-sm text-gray-400">
                {tier.features.slice(0, 3).map((feature, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SubscriptionStatus;
