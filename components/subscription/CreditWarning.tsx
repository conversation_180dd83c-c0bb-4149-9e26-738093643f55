import React from 'react';
import { Alert, AlertDescription } from '../ui/alert';
import { Button } from '@/components/ui/button';
import { useSubscription } from '@/components/hooks/useSubscription';
import { AlertTriangle, TrendingUp } from 'lucide-react';

interface CreditWarningProps {
  creditsNeeded?: number;
  onUpgrade?: () => void;
  className?: string;
}

const CreditWarning: React.FC<CreditWarningProps> = ({
  creditsNeeded = 1,
  onUpgrade,
  className = ""
}) => {
  const {
    shouldWarnAboutCredits,
    getRemainingCredits,
    getUsageSuggestions
  } = useSubscription();

  const remainingCredits = getRemainingCredits();
  const suggestions = getUsageSuggestions();
  const shouldWarn = shouldWarnAboutCredits(creditsNeeded);

  if (!shouldWarn && suggestions.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Low Credits Warning */}
      {shouldWarn && (
        <Alert className="border-yellow-700/30 bg-yellow-900/20">
          <AlertTriangle className="h-4 w-4 text-yellow-400" />
          <AlertDescription className="text-yellow-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Low Credits Warning</p>
                <p className="text-sm text-yellow-200 mt-1">
                  You have {remainingCredits} credits remaining. Each action uses {creditsNeeded} credit(s).
                </p>
              </div>
              {onUpgrade && (
                <Button 
                  onClick={onUpgrade}
                  size="sm"
                  className="bg-yellow-600 hover:bg-yellow-700 text-white ml-4"
                >
                  Upgrade
                </Button>
              )}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Simple Credit Information */}
      {creditsNeeded > 0 && (
        <Alert className="border-blue-700/30 bg-blue-900/20">
          <AlertDescription className="text-blue-300">
            <div className="text-sm">
              <p className="font-medium">Credit Usage</p>
              <p className="text-blue-200 mt-1">
                This action will use {creditsNeeded} credit{creditsNeeded > 1 ? 's' : ''}.
              </p>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Smart Usage Suggestions */}
      {suggestions.map((suggestion, index) => (
        <Alert 
          key={index}
          className={`border ${
            suggestion.type === 'error' 
              ? 'border-red-700/30 bg-red-900/20' 
              : suggestion.type === 'warning'
              ? 'border-yellow-700/30 bg-yellow-900/20'
              : 'border-blue-700/30 bg-blue-900/20'
          }`}
        >
          <TrendingUp className={`h-4 w-4 ${
            suggestion.type === 'error' 
              ? 'text-red-400' 
              : suggestion.type === 'warning'
              ? 'text-yellow-400'
              : 'text-blue-400'
          }`} />
          <AlertDescription className={`${
            suggestion.type === 'error' 
              ? 'text-red-300' 
              : suggestion.type === 'warning'
              ? 'text-yellow-300'
              : 'text-blue-300'
          }`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Smart Suggestion</p>
                <p className="text-sm mt-1 opacity-90">
                  {suggestion.message}
                </p>
              </div>
              {suggestion.action === 'upgrade' && onUpgrade && (
                <Button 
                  onClick={onUpgrade}
                  size="sm"
                  variant="outline"
                  className="ml-4"
                >
                  Upgrade Plan
                </Button>
              )}
            </div>
          </AlertDescription>
        </Alert>
      ))}
    </div>
  );
};

export default CreditWarning;
