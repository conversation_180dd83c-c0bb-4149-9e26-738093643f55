import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileText, Shield, AlertTriangle } from 'lucide-react';

interface TermsOfServiceProps {
  onBack: () => void;
}

const TermsOfService = ({ onBack }: TermsOfServiceProps) => {
  const sections = [
    {
      title: "Acceptance of Terms",
      icon: <FileText className="w-5 h-5" />,
      content: [
        "By accessing and using YouTube Subtitle Extractor, you accept and agree to be bound by the terms and provision of this agreement.",
        "If you do not agree to abide by the above, please do not use this service."
      ]
    },
    {
      title: "Service Description",
      icon: <Shield className="w-5 h-5" />,
      content: [
        "YouTube Subtitle Extractor is a free tool that extracts publicly available subtitle data from YouTube videos.",
        "The service processes subtitle files that are already publicly accessible through YouTube's platform.",
        "We do not store, cache, or retain any video content, subtitle data, or personal information.",
        "All processing happens in real-time and data is immediately discarded after processing."
      ]
    },
    {
      title: "Acceptable Use",
      icon: <AlertTriangle className="w-5 h-5" />,
      content: [
        "You may use this service only for lawful purposes and in accordance with these Terms.",
        "You agree not to use the service for any commercial purposes without explicit permission.",
        "You must respect YouTube's Terms of Service and applicable copyright laws.",
        "Bulk extraction or automated usage that may impact service availability is prohibited.",
        "You are responsible for ensuring your use complies with applicable laws in your jurisdiction."
      ]
    },
    {
      title: "Intellectual Property",
      icon: <Shield className="w-5 h-5" />,
      content: [
        "The subtitle content extracted belongs to the original content creators and YouTube.",
        "We do not claim ownership of any extracted subtitle content.",
        "Users are responsible for respecting copyright and intellectual property rights.",
        "Fair use principles should guide your use of extracted subtitle content."
      ]
    },
    {
      title: "Disclaimer of Warranties",
      icon: <AlertTriangle className="w-5 h-5" />,
      content: [
        "This service is provided 'as is' without any representations or warranties.",
        "We do not guarantee the accuracy, completeness, or quality of extracted subtitles.",
        "We are not responsible for any errors in subtitle content or formatting.",
        "Service availability is not guaranteed and may be interrupted for maintenance."
      ]
    },
    {
      title: "Limitation of Liability",
      icon: <Shield className="w-5 h-5" />,
      content: [
        "In no event shall YouTube Subtitle Extractor be liable for any indirect, incidental, special, consequential, or punitive damages.",
        "Our total liability shall not exceed the amount you paid for the service (which is currently $0).",
        "You acknowledge that you use this service at your own risk."
      ]
    },
    {
      title: "Privacy and Data",
      icon: <Shield className="w-5 h-5" />,
      content: [
        "We do not collect, store, or process personal data.",
        "No cookies are used for tracking purposes.",
        "All subtitle processing happens locally in your browser or temporarily on our servers.",
        "We do not share any data with third parties."
      ]
    },
    {
      title: "Changes to Terms",
      icon: <FileText className="w-5 h-5" />,
      content: [
        "We reserve the right to modify these terms at any time.",
        "Changes will be effective immediately upon posting on this page.",
        "Continued use of the service constitutes acceptance of modified terms."
      ]
    },
    {
      title: "Contact Information",
      icon: <FileText className="w-5 h-5" />,
      content: [
        "For questions about these Terms of Service, please contact us through our support channels.",
        "We will respond to legitimate inquiries within a reasonable timeframe."
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <Button
            variant="ghost"
            onClick={onBack}
            className="text-white hover:text-purple-400"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
          
          <div className="text-right">
            <p className="text-gray-400 text-sm">Last updated: {new Date().toLocaleDateString()}</p>
          </div>
        </motion.div>

        {/* Title */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Terms of Service
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Please read these terms carefully before using YouTube Subtitle Extractor
          </p>
        </motion.div>

        {/* Terms Sections */}
        <div className="space-y-6">
          {sections.map((section, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * (index + 2) }}
            >
              <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center space-x-3">
                    <div className="text-purple-400">
                      {section.icon}
                    </div>
                    <span>{section.title}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {section.content.map((paragraph, pIndex) => (
                      <p key={pIndex} className="text-gray-300 leading-relaxed">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-12 text-center"
        >
          <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
            <CardContent className="p-6">
              <p className="text-gray-300 mb-4">
                By using YouTube Subtitle Extractor, you acknowledge that you have read, understood, 
                and agree to be bound by these Terms of Service.
              </p>
              <p className="text-sm text-gray-400">
                These terms are effective as of {new Date().toLocaleDateString()} and will remain in effect 
                except with respect to any changes in their provisions in the future.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default TermsOfService;
