import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Crown, Zap, Star } from 'lucide-react';
import { PRICING_TIERS, type PricingTier } from '@/lib/stripe';
import { useAuth } from '@/components/contexts/AuthContext';
import { useSubscription } from '@/components/hooks/useSubscription';
import { stripePromise } from '@/lib/stripe';
import toast from 'react-hot-toast';

interface PricingCardProps {
  tier: PricingTier;
  isCurrentPlan?: boolean;
  onSelectPlan?: (tier: PricingTier) => void;
}

const PricingCard: React.FC<PricingCardProps> = ({ tier, isCurrentPlan = false, onSelectPlan }) => {
  const { user } = useAuth();
  const { createCheckoutSession } = useSubscription();
  const [loading, setLoading] = React.useState(false);

  const tierData = PRICING_TIERS[tier];

  const handleSelectPlan = async () => {
    if (!user) {
      toast.error('Please sign in to subscribe');
      return;
    }

    if (isCurrentPlan) {
      toast('This is your current plan');
      return;
    }

    try {
      setLoading(true);
      const sessionId = await createCheckoutSession(tier);
      
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error('Stripe failed to load');
      }

      const { error } = await stripe.redirectToCheckout({ sessionId });
      
      if (error) {
        throw error;
      }
    } catch (err) {
      console.error('Error selecting plan:', err);
      toast.error('Failed to start checkout process');
    } finally {
      setLoading(false);
    }
  };

  const getIcon = () => {
    switch (tier) {
      case 'starter':
        return <Zap className="w-6 h-6 text-blue-500" />;
      case 'pro':
        return <Star className="w-6 h-6 text-purple-500" />;
      case 'creator':
        return <Crown className="w-6 h-6 text-yellow-500" />;
      case 'enterprise':
        return <Crown className="w-6 h-6 text-gold-500" />;
      default:
        return <Zap className="w-6 h-6" />;
    }
  };

  const getGradient = () => {
    switch (tier) {
      case 'starter':
        return 'from-blue-500 to-cyan-500';
      case 'pro':
        return 'from-purple-500 to-pink-500';
      case 'creator':
        return 'from-yellow-500 to-orange-500';
      case 'enterprise':
        return 'from-amber-500 to-yellow-500';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="relative"
    >
      <Card className={`relative overflow-hidden ${tierData.popular ? 'border-purple-500 shadow-lg shadow-purple-500/20' : 'border-slate-700'} bg-slate-800/80 backdrop-blur-sm`}>
        {tierData.popular && (
          <div className="absolute top-0 left-0 right-0">
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-center py-2 text-sm font-medium">
              Most Popular
            </div>
          </div>
        )}
        
        <CardHeader className={tierData.popular ? 'pt-12' : 'pt-6'}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getIcon()}
              <CardTitle className="text-white">{tierData.name}</CardTitle>
            </div>
            {isCurrentPlan && (
              <Badge variant="secondary" className="bg-green-600 text-white">
                Current Plan
              </Badge>
            )}
          </div>
          <CardDescription className="text-gray-300">
            <div className="flex items-baseline gap-1">
              <span className="text-3xl font-bold text-white">${tierData.price}</span>
              <span className="text-gray-400">/month</span>
            </div>
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <ul className="space-y-3">
            {tierData.features.map((feature, index) => (
              <li key={index} className="flex items-start gap-2">
                <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-300 text-sm">{feature}</span>
              </li>
            ))}
          </ul>

          {/* Credit Information */}
          <div className="pt-4 border-t border-slate-700">
            <h4 className="text-sm font-medium text-white mb-2">High-Bandwidth Proxy Model</h4>
            <div className="space-y-2 text-sm text-gray-400">
              <div className="flex justify-between">
                <span>Monthly credits:</span>
                <span className="text-white font-medium">{tierData.limits.creditsPerMonth.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Total bandwidth:</span>
                <span className="text-blue-300 font-medium">~{(tierData.limits.creditsPerMonth * (tierData.limits.bandwidthPerCredit || 20))}MB</span>
              </div>
              <div className="text-xs space-y-1 bg-slate-700/30 rounded p-2">
                <div className="flex justify-between">
                  <span>Get languages:</span>
                  <span>1 credit (~20MB)</span>
                </div>
                <div className="flex justify-between">
                  <span>Download subtitles:</span>
                  <span>1 credit (~20MB)</span>
                </div>
                <div className="flex justify-between border-t border-slate-600 pt-1 font-medium text-gray-300">
                  <span>Complete extraction:</span>
                  <span>2 credits (~40MB)</span>
                </div>
              </div>
              <div className="text-xs text-gray-500 text-center space-y-1">
                <div>~{Math.floor(tierData.limits.creditsPerMonth / tierData.limits.actionsPerExtraction)} complete extractions/month</div>
                <div className="text-blue-400">Credits valid for 6 months</div>
              </div>
            </div>
          </div>
        </CardContent>

        <CardFooter>
          <Button
            onClick={handleSelectPlan}
            disabled={loading || isCurrentPlan}
            className={`w-full ${tierData.popular 
              ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700' 
              : 'bg-slate-700 hover:bg-slate-600'
            } text-white`}
          >
            {loading ? 'Processing...' : isCurrentPlan ? 'Current Plan' : `Choose ${tierData.name}`}
          </Button>
        </CardFooter>

        {/* Background gradient */}
        <div className={`absolute inset-0 bg-gradient-to-br ${getGradient()} opacity-5 pointer-events-none`} />
      </Card>
    </motion.div>
  );
};

export default PricingCard;
