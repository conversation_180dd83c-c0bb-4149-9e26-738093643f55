import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, AlertTriangle, Shield, Info, FileText } from 'lucide-react';

interface DisclaimerProps {
  onBack: () => void;
}

const Disclaimer = ({ onBack }: DisclaimerProps) => {
  const sections = [
    {
      title: "Service Purpose",
      icon: <Info className="w-5 h-5" />,
      content: [
        "DownloadYTSubtitles.com is a free tool designed to extract publicly available subtitle data from YouTube videos.",
        "We provide this service to help users access subtitle content for educational, research, and accessibility purposes.",
        "Our tool only processes subtitle files that are already publicly accessible through YouTube's platform."
      ]
    },
    {
      title: "Content Ownership",
      icon: <Shield className="w-5 h-5" />,
      content: [
        "All subtitle content extracted through our service belongs to the original video creators and YouTube.",
        "We do not claim ownership of any extracted subtitle content or the underlying video material.",
        "Users are responsible for respecting copyright laws and intellectual property rights when using extracted content.",
        "Always credit the original content creators when using their subtitle content."
      ]
    },
    {
      title: "Acceptable Use",
      icon: <FileText className="w-5 h-5" />,
      content: [
        "This service is intended for personal, educational, and non-commercial use only.",
        "Commercial use of extracted subtitles may require permission from the original content creators.",
        "Users must comply with YouTube's Terms of Service and applicable copyright laws.",
        "Fair use principles should guide your use of extracted subtitle content."
      ]
    },
    {
      title: "Service Limitations",
      icon: <AlertTriangle className="w-5 h-5" />,
      content: [
        "We cannot guarantee the availability or accuracy of subtitle content for all videos.",
        "Subtitle availability depends on whether the video creator has enabled subtitles or YouTube has generated them.",
        "Auto-generated subtitles may contain errors and should be reviewed before use.",
        "Service availability may be interrupted for maintenance or technical reasons."
      ]
    },
    {
      title: "Legal Compliance",
      icon: <Shield className="w-5 h-5" />,
      content: [
        "Users are responsible for ensuring their use of extracted content complies with local laws.",
        "Different countries may have varying copyright and fair use regulations.",
        "Educational and research use is generally more permissible than commercial use.",
        "When in doubt, seek permission from the original content creator."
      ]
    },
    {
      title: "No Warranty",
      icon: <AlertTriangle className="w-5 h-5" />,
      content: [
        "This service is provided 'as is' without any warranties or guarantees.",
        "We do not warrant the accuracy, completeness, or quality of extracted subtitles.",
        "Users assume all risks associated with the use of this service.",
        "We are not liable for any damages resulting from the use of extracted content."
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <Button
            variant="ghost"
            onClick={onBack}
            className="text-white hover:text-purple-400"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
          
          <div className="text-right">
            <p className="text-gray-400 text-sm">Last updated: {new Date().toLocaleDateString()}</p>
          </div>
        </motion.div>

        {/* Title */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Disclaimer
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Important information about using DownloadYTSubtitles.com responsibly and legally
          </p>
        </motion.div>

        {/* Important Notice */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <Card className="bg-orange-900/20 border-orange-500/30">
            <CardContent className="p-6">
              <div className="flex items-start space-x-4">
                <AlertTriangle className="w-8 h-8 text-orange-400 flex-shrink-0 mt-1" />
                <div>
                  <h3 className="text-lg font-semibold text-orange-400 mb-2">Important Notice</h3>
                  <p className="text-gray-300">
                    <strong>Use Responsibly:</strong> This tool extracts publicly available subtitle data. 
                    Users are responsible for ensuring their use complies with copyright laws and YouTube's Terms of Service. 
                    Always respect content creators' rights and give proper attribution.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Disclaimer Sections */}
        <div className="space-y-6">
          {sections.map((section, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * (index + 3) }}
            >
              <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center space-x-3">
                    <div className="text-purple-400">
                      {section.icon}
                    </div>
                    <span>{section.title}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {section.content.map((paragraph, pIndex) => (
                      <p key={pIndex} className="text-gray-300 leading-relaxed">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-12 text-center"
        >
          <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
            <CardContent className="p-6">
              <p className="text-gray-300 mb-4">
                By using DownloadYTSubtitles.com, you acknowledge that you have read, understood, 
                and agree to comply with this disclaimer and use the service responsibly.
              </p>
              <p className="text-sm text-gray-400">
                This disclaimer is effective as of {new Date().toLocaleDateString()} and may be updated 
                from time to time to reflect changes in our service or legal requirements.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default Disclaimer;
