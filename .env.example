# YouTube Subtitle Extractor Environment Variables

# Proxy Configuration (for YouTube extraction)
SOCKET_URL='your_proxy_url_here'

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SUPABASE_PROJECT_ID=your_supabase_project_id

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Application Configuration
NODE_ENV=development
PORT=3001

# Credit Pack Price IDs (from Stripe - One-time payments)
NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID=price_starter_onetime
NEXT_PUBLIC_STRIPE_PRO_PRICE_ID=price_pro_onetime
NEXT_PUBLIC_STRIPE_CREATOR_PRICE_ID=price_creator_onetime
NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID=price_enterprise_onetime
