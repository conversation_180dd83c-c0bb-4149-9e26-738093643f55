import ytdl from '@distube/ytdl-core';
import { SocksProxyAgent } from 'socks-proxy-agent';
import { consumeCredits, getUserFromRequest } from '../../../lib/credit-manager.js';


// Helper function to get language name from code
const getLanguageName = (code) => {
  const languageMap = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'zh': 'Chinese',
    'ar': 'Arabic',
    'hi': 'Hindi',
    'tr': 'Turkish',
    'pl': 'Polish',
    'nl': 'Dutch',
    'sv': 'Swedish',
    'da': 'Danish',
    'no': 'Norwegian',
    'fi': 'Finnish',
    'cs': 'Czech',
    'hu': 'Hungarian',
    'ro': 'Romanian',
    'bg': 'Bulgarian',
    'hr': 'Croatian',
    'sk': 'Slovak',
    'sl': 'Slovenian',
    'et': 'Estonian',
    'lv': 'Latvian',
    'lt': 'Lithuanian',
    'uk': 'Ukrainian',
    'el': 'Greek',
    'he': 'Hebrew',
    'th': 'Thai',
    'vi': 'Vietnamese',
    'id': 'Indonesian',
    'ms': 'Malay',
    'tl': 'Filipino',
    'sw': 'Swahili',
    'af': 'Afrikaans'
  };
  
  return languageMap[code] || code.toUpperCase();
};

// SOCKS5 or HTTPS proxy URI format
const agent = process.env.SOCKET_ENABLED ? ytdl.createProxyAgent(process.env.SOCKET_URL) : undefined;
// const agent = new SocksProxyAgent(
// 	'socks5://*************:8443'
// );
export default async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const { videoId } = req.query;

    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    // Authenticate user and consume credits
    const { userId, error: authError } = await getUserFromRequest(req);
    if (authError) {
      return res.status(401).json({ error: authError });
    }

    // Consume 1 credit for getting available languages
    const creditResult = await consumeCredits(userId, 1, 'Get available languages');
    if (!creditResult.success) {
      return res.status(403).json({ error: creditResult.message });
    }

    console.log(`Fetching subtitle languages for video: ${videoId}`);

    // Get video info including available subtitles
    const videoInfo = await ytdl.getInfo(`https://www.youtube.com/watch?v=${videoId}`);

    const availableLanguages = [];

    // Get available subtitle tracks
    if (videoInfo.player_response?.captions?.playerCaptionsTracklistRenderer?.captionTracks) {
      const captionTracks = videoInfo.player_response.captions.playerCaptionsTracklistRenderer.captionTracks;

      captionTracks.forEach(track => {
        const langCode = track.languageCode;
        const langName = track.name?.simpleText || getLanguageName(langCode);
        const isAutoGenerated = track.kind === 'asr';

        availableLanguages.push({
          code: langCode,
          name: langName,
          isAutoGenerated: isAutoGenerated
        });
      });
    }

    if (availableLanguages.length === 0) {
      return res.status(404).json({ error: 'No subtitles available for this video' });
    }

    res.status(200).json({
      videoId,
      title: videoInfo.videoDetails?.title || 'YouTube Video',
      thumbnail: videoInfo.videoDetails?.thumbnails?.[0]?.url || `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
      languages: availableLanguages,
      creditsUsed: 1,
      remainingCredits: creditResult.remainingCredits
    });

  } catch (error) {
    console.error('Error fetching subtitle languages:', error);
    res.status(500).json({
      error: 'Failed to fetch subtitle languages',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
