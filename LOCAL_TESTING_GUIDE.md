# Local Development & Testing Guide

## 🚀 Quick Start

### 1. Start Development Server
```bash
npm run dev
```
This starts the Next.js development server:
- **Application**: http://localhost:3000

### 2. Run Tests
```bash
node test-local.js
```

### 3. Access Application
- **Main App**: http://localhost:3000
- **API Health**: http://localhost:3000/api/health

## 🔧 Fixed Issues

### ✅ Next.js Migration Complete
**Problem**: Project was using Vite which is not optimal for serverless deployment
**Solution**: Migrated to Next.js for better Vercel integration and serverless functions

### ✅ Supabase Environment Variables
**Problem**: Missing Supabase configuration causing app initialization failure
**Solution**: Added fallback values for development environment

### ✅ Environment Variables
**Problem**: Vite environment variables (VITE_*) not working with Next.js
**Solution**: Updated all environment variables to use NEXT_PUBLIC_ prefix for client-side access

## 🧪 Testing Checklist

### Client-Side Testing
- [ ] Landing page loads without errors
- [ ] Navigation between sections works
- [ ] Responsive design on mobile/desktop
- [ ] No console errors in browser dev tools
- [ ] All animations and interactions work

### Server-Side Testing
- [ ] Health endpoint responds: `curl http://localhost:3000/api/health`
- [ ] YouTube API endpoints accessible
- [ ] CORS headers properly configured
- [ ] Proxy configuration working

### Integration Testing
- [ ] YouTube URL validation works
- [ ] Subtitle language detection works
- [ ] Subtitle download functionality works
- [ ] Error handling displays properly

## 🐛 Debugging Tips

### If UI doesn't render:
1. Check browser console (F12 → Console)
2. Verify no network errors (F12 → Network)
3. Check terminal for server errors
4. Restart development server

### If API doesn't work:
1. Test health endpoint: http://localhost:3000/api/health
2. Check server logs in terminal
3. Verify Next.js API routes configuration
4. Test with curl commands

### Common Issues:
- **Port conflicts**: Change ports in package.json scripts
- **Environment variables**: Check .env file configuration
- **Import errors**: Verify file paths and TypeScript types
- **CORS errors**: Check server CORS configuration

## 🌐 Environment Configuration

### Development (Current)
```env
NEXT_PUBLIC_SUPABASE_URL=https://placeholder.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=placeholder-key
```

### Production
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-real-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

## 📦 Build & Deploy

### Local Build Test
```bash
npm run build
npm run start
```

### Vercel Deployment
```bash
vercel dev    # Test locally with Vercel
vercel --prod # Deploy to production
```

## 🎯 Next Steps

1. **Test Core Functionality**: Try extracting subtitles from a YouTube video
2. **UI/UX Testing**: Test all user interactions and flows
3. **Performance Testing**: Check loading times and responsiveness
4. **Error Handling**: Test with invalid URLs and edge cases
5. **Mobile Testing**: Verify mobile responsiveness and touch interactions

## 📞 Support

If you encounter any issues:
1. Check this guide first
2. Review browser console errors
3. Check server terminal output
4. Test with the provided test script
