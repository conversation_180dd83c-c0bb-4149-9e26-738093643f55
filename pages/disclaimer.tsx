import Head from 'next/head';
import { useRouter } from 'next/router';
import Header from '../components/Header';
import Footer from '../components/Footer';
import Disclaimer from '../components/Disclaimer';

export default function DisclaimerPage() {
  const router = useRouter();

  const handleBackToHome = () => {
    router.push('/');
  };

  const handleNavigate = (view: string) => {
    router.push(`/${view}`);
  };

  const handleFeedback = () => {
    if (typeof window !== 'undefined') {
      window.open('https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog', '_blank');
    }
  };

  const handleTermsClick = () => {
    router.push('/terms');
  };

  const handlePrivacyClick = () => {
    router.push('/privacy');
  };

  const handleDisclaimerClick = () => {
    router.push('/disclaimer');
  };

  return (
    <>
      <Head>
        <title>Disclaimer - DownloadYTSubtitles</title>
        <meta name="description" content="Disclaimer for DownloadYTSubtitles - YouTube subtitle extractor service." />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen font-sans">
        <Header
          currentView="disclaimer"
          onNavigate={handleNavigate}
          onFeedback={handleFeedback}
        />
        
        <Disclaimer onBack={handleBackToHome} />
        
        <Footer
          onTermsClick={handleTermsClick}
          onPrivacyClick={handlePrivacyClick}
          onDisclaimerClick={handleDisclaimerClick}
        />
      </div>
    </>
  );
}
