export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // This endpoint handles the OAuth callback from Google
    // In a client-side app, Supabase handles this automatically
    // This is mainly for server-side processing if needed
    
    const { code, state, error } = req.query;

    if (error) {
      // Redirect to error page
      return res.redirect(`${process.env.SITE_URL || 'http://localhost:5173'}/?error=${encodeURIComponent(error)}`);
    }

    if (code) {
      // Redirect to success page - let the client handle the session
      return res.redirect(`${process.env.SITE_URL || 'http://localhost:5173'}/dashboard`);
    }

    // Default redirect
    res.redirect(`${process.env.SITE_URL || 'http://localhost:5173'}/`);

  } catch (error) {
    console.error('Auth callback error:', error);
    res.redirect(`${process.env.SITE_URL || 'http://localhost:5173'}/?error=auth_error`);
  }
}
