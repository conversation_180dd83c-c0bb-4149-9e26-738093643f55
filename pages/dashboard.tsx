import Head from 'next/head';
import { useRouter } from 'next/router';
import UserDashboard from '../components/dashboard/UserDashboard';

export default function Dashboard() {
  const router = useRouter();

  const handleBackToHome = () => {
    router.push('/');
  };

  const handleNavigate = (view: string) => {
    router.push(`/${view}`);
  };

  return (
    <>
      <Head>
        <title>Dashboard - DownloadYTSubtitles</title>
        <meta name="description" content="User dashboard for DownloadYTSubtitles - YouTube subtitle extractor service." />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <UserDashboard onBack={handleBackToHome} onNavigate={handleNavigate} />
    </>
  );
}
