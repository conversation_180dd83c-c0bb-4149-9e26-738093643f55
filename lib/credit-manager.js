import { createServerSupabaseClient } from './supabase.js';
import { PRICING_TIERS } from './stripe.js';

/**
 * Credit Management System
 * Handles credit consumption and validation for YouTube actions
 */

/**
 * Check if user has enough credits and consume them
 * @param {string} userId - User ID
 * @param {number} creditsNeeded - Number of credits to consume
 * @param {string} action - Action description for logging
 * @returns {Promise<{success: boolean, message: string, remainingCredits?: number}>}
 */
export async function consumeCredits(userId, creditsNeeded = 1, action = 'YouTube action') {
  const supabase = createServerSupabaseClient();

  try {
    // Get user's subscription and usage
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (subError || !subscription) {
      return {
        success: false,
        message: 'No active subscription found. Please subscribe to a plan.'
      };
    }

    // Get tier limits
    const tier = PRICING_TIERS[subscription.tier];
    if (!tier) {
      return {
        success: false,
        message: 'Invalid subscription tier.'
      };
    }

    // Get current usage
    const { data: usage, error: usageError } = await supabase
      .from('usage_stats')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (usageError) {
      // Create usage stats if they don't exist
      const { error: createError } = await supabase
        .from('usage_stats')
        .insert({
          user_id: userId,
          credits_used_this_month: 0,
          videos_extracted_this_month: 0,
          last_reset_date: new Date().toISOString()
        });

      if (createError) {
        console.error('Error creating usage stats:', createError);
        return {
          success: false,
          message: 'Failed to initialize usage tracking.'
        };
      }

      // Retry getting usage
      const { data: newUsage, error: retryError } = await supabase
        .from('usage_stats')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (retryError) {
        return {
          success: false,
          message: 'Failed to get usage statistics.'
        };
      }

      usage = newUsage;
    }

    // Check if user has enough credits
    const currentCreditsUsed = usage.credits_used_this_month || 0;
    const availableCredits = tier.limits.creditsPerMonth - currentCreditsUsed;

    if (availableCredits < creditsNeeded) {
      return {
        success: false,
        message: `Insufficient credits. You need ${creditsNeeded} credits but only have ${availableCredits} remaining.`
      };
    }

    // Consume credits
    const newCreditsUsed = currentCreditsUsed + creditsNeeded;
    const { error: updateError } = await supabase
      .from('usage_stats')
      .update({
        credits_used_this_month: newCreditsUsed,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (updateError) {
      console.error('Error updating usage stats:', updateError);
      return {
        success: false,
        message: 'Failed to update credit usage.'
      };
    }

    const remainingCredits = tier.limits.creditsPerMonth - newCreditsUsed;

    console.log(`Credits consumed: ${creditsNeeded} for ${action}. User ${userId} has ${remainingCredits} credits remaining.`);

    return {
      success: true,
      message: `${action} completed. ${creditsNeeded} credit(s) used.`,
      remainingCredits
    };

  } catch (error) {
    console.error('Error in consumeCredits:', error);
    return {
      success: false,
      message: 'Internal error while processing credits.'
    };
  }
}

/**
 * Check if user can perform an action without consuming credits
 * @param {string} userId - User ID
 * @param {number} creditsNeeded - Number of credits needed
 * @returns {Promise<{canPerform: boolean, message: string, remainingCredits?: number}>}
 */
export async function checkCredits(userId, creditsNeeded = 1) {
  const supabase = createServerSupabaseClient();

  try {
    // Get user's subscription and usage
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (subError || !subscription) {
      return {
        canPerform: false,
        message: 'No active subscription found.'
      };
    }

    // Get tier limits
    const tier = PRICING_TIERS[subscription.tier];
    if (!tier) {
      return {
        canPerform: false,
        message: 'Invalid subscription tier.'
      };
    }

    // Get current usage
    const { data: usage, error: usageError } = await supabase
      .from('usage_stats')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (usageError) {
      return {
        canPerform: false,
        message: 'Failed to get usage statistics.'
      };
    }

    // Check if user has enough credits
    const currentCreditsUsed = usage.credits_used_this_month || 0;
    const availableCredits = tier.limits.creditsPerMonth - currentCreditsUsed;

    return {
      canPerform: availableCredits >= creditsNeeded,
      message: availableCredits >= creditsNeeded 
        ? `You have ${availableCredits} credits available.`
        : `Insufficient credits. You need ${creditsNeeded} but only have ${availableCredits}.`,
      remainingCredits: availableCredits
    };

  } catch (error) {
    console.error('Error in checkCredits:', error);
    return {
      canPerform: false,
      message: 'Internal error while checking credits.'
    };
  }
}

/**
 * Get user authentication from request headers
 * @param {Object} req - Request object
 * @returns {Promise<{userId: string | null, error: string | null}>}
 */
export async function getUserFromRequest(req) {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return {
      userId: null,
      error: 'Missing or invalid authorization header'
    };
  }

  const token = authHeader.substring(7);
  const supabase = createServerSupabaseClient();

  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return {
        userId: null,
        error: 'Invalid or expired token'
      };
    }

    return {
      userId: user.id,
      error: null
    };
  } catch (error) {
    console.error('Error getting user from token:', error);
    return {
      userId: null,
      error: 'Failed to authenticate user'
    };
  }
}
