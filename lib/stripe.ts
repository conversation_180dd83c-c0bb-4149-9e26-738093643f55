import { loadStripe } from '@stripe/stripe-js';

const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

if (!stripePublishableKey && typeof window !== 'undefined') {
  console.warn('Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable');
}

export const stripePromise = stripePublishableKey ? loadStripe(stripePublishableKey) : null;

// Pricing configuration - High-Bandwidth Proxy Credit System
// Credit system: 1 credit = 1 API call (~20MB bandwidth, $0.10 proxy cost)
// - Getting available languages = 1 credit
// - Downloading subtitles = 1 credit
// - Total per complete video extraction = 2 credits
// Proxy server costs $5/GB (~$0.10 per API call), priced at $0.15+ per credit for profitability
export const PRICING_TIERS = {
  starter: {
    name: 'Starter',
    price: 10,
    priceId: process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID || 'price_starter_monthly',
    features: [
      '50 credits/month (~25 complete extractions)',
      'VTT and TXT format downloads',
      'Auto-generated caption support',
      'Basic language detection',
      'Email support',
      'Credits valid for 6 months'
    ],
    limits: {
      creditsPerMonth: 50,
      creditsPerAction: 1, // 1 credit per API call (~20MB bandwidth)
      actionsPerExtraction: 2, // Get languages + Download = 2 actions
      bandwidthPerCredit: 20, // MB per credit
      costPerCredit: 0.20 // $0.20 per credit
    },
    popular: false
  },
  pro: {
    name: 'Pro',
    price: 30,
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRO_PRICE_ID || 'price_pro_monthly',
    features: [
      '200 credits/month (~100 complete extractions)',
      'All format downloads (VTT, TXT)',
      'Manual and auto-generated captions',
      'Advanced language detection',
      'Batch processing',
      'Priority support',
      'Credits valid for 6 months'
    ],
    limits: {
      creditsPerMonth: 200,
      creditsPerAction: 1, // 1 credit per API call (~20MB bandwidth)
      actionsPerExtraction: 2, // Get languages + Download = 2 actions
      bandwidthPerCredit: 20, // MB per credit
      costPerCredit: 0.15 // $0.15 per credit
    },
    popular: true
  },
  creator: {
    name: 'Creator',
    price: 75,
    priceId: process.env.NEXT_PUBLIC_STRIPE_CREATOR_PRICE_ID || 'price_creator_monthly',
    features: [
      '600 credits/month (~300 complete extractions)',
      'All format downloads',
      'Manual and auto-generated captions',
      'Advanced language detection',
      'Batch processing',
      'API access (coming soon)',
      'Custom integrations',
      'Priority support',
      'Credits valid for 6 months'
    ],
    limits: {
      creditsPerMonth: 600,
      creditsPerAction: 1, // 1 credit per API call (~20MB bandwidth)
      actionsPerExtraction: 2, // Get languages + Download = 2 actions
      bandwidthPerCredit: 20, // MB per credit
      costPerCredit: 0.125 // $0.125 per credit
    },
    popular: false
  },
  enterprise: {
    name: 'Enterprise',
    price: 150,
    priceId: process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID || 'price_enterprise_monthly',
    features: [
      '1200 credits/month (~600 complete extractions)',
      'All format downloads',
      'Manual and auto-generated captions',
      'Advanced language detection',
      'Batch processing',
      'Full API access',
      'Custom integrations',
      'Dedicated support',
      'Credits valid for 6 months',
      'Volume discounts available'
    ],
    limits: {
      creditsPerMonth: 1200,
      creditsPerAction: 1, // 1 credit per API call (~20MB bandwidth)
      actionsPerExtraction: 2, // Get languages + Download = 2 actions
      bandwidthPerCredit: 20, // MB per credit
      costPerCredit: 0.125 // $0.125 per credit
    },
    popular: false
  }
} as const;

export type PricingTier = keyof typeof PRICING_TIERS;
