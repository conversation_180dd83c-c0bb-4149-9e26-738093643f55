import { loadStripe } from '@stripe/stripe-js';

const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

if (!stripePublishableKey && typeof window !== 'undefined') {
  console.warn('Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable');
}

export const stripePromise = stripePublishableKey ? loadStripe(stripePublishableKey) : null;

// Pricing configuration - Credit-based system
// Credit system: 1 credit = 1 YouTube action
// - Getting available languages = 1 credit
// - Downloading subtitles = 1 credit
// - Total per complete video extraction = 2 credits
// Proxy server costs $5 for 1GB, optimized for cost-effective usage
export const PRICING_TIERS = {
  starter: {
    name: 'Starter',
    price: 9,
    priceId: process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID || 'price_starter_monthly',
    features: [
      '100 credits/month (~50 complete extractions)',
      'VTT and TXT format downloads',
      'Auto-generated caption support',
      'Basic language detection',
      'Email support'
    ],
    limits: {
      creditsPerMonth: 100,
      creditsPerAction: 1, // 1 credit per YouTube action
      actionsPerExtraction: 2 // Get languages + Download = 2 actions
    },
    popular: false
  },
  pro: {
    name: 'Pro',
    price: 19,
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRO_PRICE_ID || 'price_pro_monthly',
    features: [
      '500 credits/month (~250 complete extractions)',
      'All format downloads (VTT, TXT)',
      'Manual and auto-generated captions',
      'Advanced language detection',
      'Batch processing',
      'Priority support'
    ],
    limits: {
      creditsPerMonth: 500,
      creditsPerAction: 1, // 1 credit per YouTube action
      actionsPerExtraction: 2 // Get languages + Download = 2 actions
    },
    popular: true
  },
  premium: {
    name: 'Premium',
    price: 39,
    priceId: process.env.NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID || 'price_premium_monthly',
    features: [
      '1500 credits/month (~750 complete extractions)',
      'All format downloads',
      'Manual and auto-generated captions',
      'Advanced language detection',
      'Batch processing',
      'API access (coming soon)',
      'Custom integrations',
      'Priority support'
    ],
    limits: {
      creditsPerMonth: 1500,
      creditsPerAction: 1, // 1 credit per YouTube action
      actionsPerExtraction: 2 // Get languages + Download = 2 actions
    },
    popular: false
  }
} as const;

export type PricingTier = keyof typeof PRICING_TIERS;
