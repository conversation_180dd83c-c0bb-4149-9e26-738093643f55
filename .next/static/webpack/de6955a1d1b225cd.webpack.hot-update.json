{"c": ["main", "pages/_app", "pages/faq", "pages/extractor", "pages/index", "pages/pricing", "webpack"], "r": ["pages/faq", "pages/index"], "m": ["(pages-dir-browser)/./components/FAQ.tsx", "(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js", "(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js", "(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js", "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fdineshs%2FDocuments%2FDev%2FProjects%2FYTSubtitleExtractor%2FYTSubtitleExtractor%2Fpages%2Ffaq.tsx&page=%2Ffaq!", "(pages-dir-browser)/./pages/faq.tsx", "(pages-dir-browser)/__barrel_optimize__?names=ChevronDown,ChevronUp,Download,FileText,Globe,HelpCircle,Shield,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "(pages-dir-browser)/./components/HowToUse.tsx", "(pages-dir-browser)/./components/LandingPage.tsx", "(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js", "(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/link.js", "(pages-dir-browser)/./node_modules/lucide-react/dist/esm/icons/users.js", "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fdineshs%2FDocuments%2FDev%2FProjects%2FYTSubtitleExtractor%2FYTSubtitleExtractor%2Fpages%2Findex.tsx&page=%2F!", "(pages-dir-browser)/./pages/index.tsx", "(pages-dir-browser)/__barrel_optimize__?names=ArrowRight,CheckCircle,Download,Eye,Link,Search!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "(pages-dir-browser)/__barrel_optimize__?names=ArrowRight,Crown,Download,Globe,Play,Shield,Star,Users,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js"]}