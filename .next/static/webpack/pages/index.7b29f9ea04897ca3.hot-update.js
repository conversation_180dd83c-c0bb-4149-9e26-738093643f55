"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./lib/stripe.ts":
/*!***********************!*\
  !*** ./lib/stripe.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PRICING_TIERS: () => (/* binding */ PRICING_TIERS),\n/* harmony export */   stripePromise: () => (/* binding */ stripePromise)\n/* harmony export */ });\n/* harmony import */ var _stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @stripe/stripe-js */ \"(pages-dir-browser)/./node_modules/@stripe/stripe-js/lib/index.mjs\");\n\nconst stripePublishableKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzYXhpaWFscmhwcWRsY3BtZXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA5NDU3MCwiZXhwIjoyMDY1NjcwNTcwfQ.pPJz2h3Y-tXgzFuKX2hMYPEnqoaHd_p1G5dNVbQSccg\";\nif (!stripePublishableKey && \"object\" !== 'undefined') {\n    console.warn('Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable');\n}\nconst stripePromise = stripePublishableKey ? (0,_stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__.loadStripe)(stripePublishableKey) : null;\n// Pricing configuration - Credit-based system\n// Credit system: 1 credit = 1 API call\n// - Getting available languages = 1 credit\n// - Downloading subtitles = 1 credit\n// - Total per complete video extraction = 2 credits\nconst PRICING_TIERS = {\n    starter: {\n        name: 'Starter',\n        price: 10,\n        priceId: \"price_1Rbz9CG3dq2cZKhWCnIVHUx6\" || 0,\n        features: [\n            '50 credits/month (~25 complete extractions)',\n            'VTT and TXT format downloads',\n            'Auto-generated caption support',\n            'Basic language detection',\n            'Email support',\n            'Credits valid for 6 months'\n        ],\n        limits: {\n            creditsPerMonth: 50,\n            creditsPerAction: 1,\n            actionsPerExtraction: 2 // Get languages + Download = 2 actions\n        },\n        popular: false\n    },\n    pro: {\n        name: 'Pro',\n        price: 30,\n        priceId: \"price_1RbzCCG3dq2cZKhWS3MlXiCZ\" || 0,\n        features: [\n            '200 credits/month (~100 complete extractions)',\n            'All format downloads (VTT, TXT)',\n            'Manual and auto-generated captions',\n            'Advanced language detection',\n            'Batch processing',\n            'Priority support',\n            'Credits valid for 6 months'\n        ],\n        limits: {\n            creditsPerMonth: 200,\n            creditsPerAction: 1,\n            actionsPerExtraction: 2 // Get languages + Download = 2 actions\n        },\n        popular: true\n    },\n    creator: {\n        name: 'Creator',\n        price: 75,\n        priceId: \"price_1122334455\" || 0,\n        features: [\n            '600 credits/month (~300 complete extractions)',\n            'All format downloads',\n            'Manual and auto-generated captions',\n            'Advanced language detection',\n            'Batch processing',\n            'API access (coming soon)',\n            'Custom integrations',\n            'Priority support',\n            'Credits valid for 6 months'\n        ],\n        limits: {\n            creditsPerMonth: 600,\n            creditsPerAction: 1,\n            actionsPerExtraction: 2 // Get languages + Download = 2 actions\n        },\n        popular: false\n    },\n    enterprise: {\n        name: 'Enterprise',\n        price: 150,\n        priceId: \"price_5544332211\" || 0,\n        features: [\n            '1200 credits/month (~600 complete extractions)',\n            'All format downloads',\n            'Manual and auto-generated captions',\n            'Advanced language detection',\n            'Batch processing',\n            'Full API access',\n            'Custom integrations',\n            'Dedicated support',\n            'Credits valid for 6 months',\n            'Volume discounts available'\n        ],\n        limits: {\n            creditsPerMonth: 1200,\n            creditsPerAction: 1,\n            actionsPerExtraction: 2 // Get languages + Download = 2 actions\n        },\n        popular: false\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./lib/stripe.ts\n"));

/***/ })

});