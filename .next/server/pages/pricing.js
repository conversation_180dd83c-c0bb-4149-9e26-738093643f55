/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/pricing";
exports.ids = ["pages/pricing"];
exports.modules = {

/***/ "(pages-dir-node)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-icons */ \"@radix-ui/react-icons\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_HelpCircle_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,HelpCircle,Mail,Shield!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=FileText,Heart,HelpCircle,Mail,Shield!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst Footer = ({ onTermsClick, onPrivacyClick, onDisclaimerClick })=>{\n    const currentYear = new Date().getFullYear();\n    const links = {\n        product: [\n            {\n                name: 'Features',\n                href: '#features'\n            },\n            {\n                name: 'How to Use',\n                href: '#how-to-use'\n            },\n            {\n                name: 'Use Cases',\n                href: '#use-cases'\n            },\n            {\n                name: 'FAQ',\n                href: '#faq'\n            }\n        ],\n        legal: [\n            {\n                name: 'Privacy Policy',\n                onClick: onPrivacyClick\n            },\n            {\n                name: 'Terms of Service',\n                onClick: onTermsClick\n            },\n            {\n                name: 'Disclaimer',\n                onClick: onDisclaimerClick\n            }\n        ],\n        social: [\n            {\n                name: 'GitHub',\n                href: '#',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_1__.GitHubLogoIcon, {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 42\n                }, undefined)\n            },\n            {\n                name: 'Twitter',\n                href: '#',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_1__.TwitterLogoIcon, {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 43\n                }, undefined)\n            },\n            {\n                name: 'Email',\n                href: 'mailto:<EMAIL>',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_HelpCircle_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Mail, {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 78\n                }, undefined)\n            }\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-slate-900 border-t border-slate-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-8 sm:py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"col-span-1 sm:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4\",\n                                    children: [\n                                        \"YouTube Subtitle\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\",\n                                            children: [\n                                                \" \",\n                                                \"Extractor\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 sm:mb-6 max-w-md text-sm sm:text-base\",\n                                    children: \"The most advanced YouTube subtitle extraction tool. Extract, clean, and download subtitles from videos and playlists with professional-grade accuracy.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 sm:space-x-4\",\n                                    children: links.social.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                            href: social.href,\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            className: \"text-gray-400 hover:text-purple-400 transition-colors duration-200\",\n                                            \"aria-label\": social.name,\n                                            children: social.icon\n                                        }, social.name, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4\",\n                                    children: \"Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-1 sm:space-y-2\",\n                                    children: links.product.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: link.href,\n                                                className: \"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4\",\n                                    children: \"Legal\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-1 sm:space-y-2\",\n                                    children: links.legal.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: link.onClick ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: link.onClick,\n                                                className: \"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                // href={link.href}\n                                                className: \"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    whileInView: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"flex flex-col lg:flex-row justify-between items-center pt-6 sm:pt-8 mt-6 sm:mt-8 border-t border-slate-800 space-y-4 lg:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center text-gray-400 text-xs sm:text-sm text-center lg:text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"\\xa9 \",\n                                        currentYear,\n                                        \" DownloadYTSubtitles.com. Made with\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_HelpCircle_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Heart, {\n                                    className: \"w-3 h-3 sm:w-4 sm:h-4 text-red-400 mx-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"for the community.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 lg:space-x-6 text-xs sm:text-sm text-gray-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_HelpCircle_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Shield, {\n                                            className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Privacy First\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_HelpCircle_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__.HelpCircle, {\n                                            className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Free During Beta\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_HelpCircle_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__.FileText, {\n                                            className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Professional Quality\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Footer.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Footer.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-node)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Download,HelpCircle,Home,MessageSquare!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=CreditCard,Download,HelpCircle,Home,MessageSquare!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/contexts/AuthContext */ \"(pages-dir-node)/./components/contexts/AuthContext.tsx\");\n/* harmony import */ var _auth_LoginButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./auth/LoginButton */ \"(pages-dir-node)/./components/auth/LoginButton.tsx\");\n/* harmony import */ var _auth_UserMenu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./auth/UserMenu */ \"(pages-dir-node)/./components/auth/UserMenu.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__, _components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _auth_LoginButton__WEBPACK_IMPORTED_MODULE_4__, _auth_UserMenu__WEBPACK_IMPORTED_MODULE_5__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_1__, _components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _auth_LoginButton__WEBPACK_IMPORTED_MODULE_4__, _auth_UserMenu__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst Header = ({ currentView, onNavigate, onFeedback })=>{\n    const { user } = (0,_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.header, {\n        initial: {\n            opacity: 0,\n            y: -20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        className: \"bg-slate-900/95 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-14 sm:h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            className: \"flex items-center space-x-2 sm:space-x-3 cursor-pointer\",\n                            onClick: ()=>onNavigate(''),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Download, {\n                                        className: \"w-4 h-4 sm:w-6 sm:h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden xs:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-lg sm:text-xl font-bold text-white\",\n                                            children: \"DownloadYTSubtitles\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400 hidden sm:block\",\n                                            children: \"YouTube Subtitle Extractor\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"block xs:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-sm font-bold text-white\",\n                                        children: \"DYTS\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: currentView === 'landing' ? 'default' : 'ghost',\n                                    size: \"sm\",\n                                    onClick: ()=>onNavigate(''),\n                                    className: currentView === 'landing' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white',\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Home, {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: currentView === 'extractor' ? 'default' : 'ghost',\n                                    size: \"sm\",\n                                    onClick: ()=>onNavigate('extractor'),\n                                    className: currentView === 'extractor' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white',\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Download, {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Extract\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: currentView === 'faq' ? 'default' : 'ghost',\n                                    size: \"sm\",\n                                    onClick: ()=>onNavigate('faq'),\n                                    className: currentView === 'faq' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white',\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__.HelpCircle, {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"FAQ\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: currentView === 'pricing' ? 'default' : 'ghost',\n                                    size: \"sm\",\n                                    onClick: ()=>onNavigate('pricing'),\n                                    className: currentView === 'pricing' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white',\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__.CreditCard, {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Pricing\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: onFeedback,\n                                    className: \"border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-xs sm:text-sm px-2 sm:px-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MessageSquare, {\n                                            className: \"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden xs:inline\",\n                                            children: \"Feedback\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"xs:hidden\",\n                                            children: \"FB\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined),\n                                user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_UserMenu__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    onNavigate: onNavigate\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_LoginButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"border-purple-600 text-purple-400 hover:bg-purple-600 hover:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>onNavigate('extractor'),\n                                        className: \"text-gray-300 hover:text-white p-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Download_HelpCircle_Home_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Download, {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden pb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-2 overflow-x-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: currentView === 'landing' ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>onNavigate(''),\n                                className: `text-xs px-3 py-2 ${currentView === 'landing' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white'}`,\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: currentView === 'extractor' ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>onNavigate('extractor'),\n                                className: `text-xs px-3 py-2 ${currentView === 'extractor' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white'}`,\n                                children: \"Extract\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: currentView === 'faq' ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>onNavigate('faq'),\n                                className: `text-xs px-3 py-2 ${currentView === 'faq' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white'}`,\n                                children: \"FAQ\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: currentView === 'pricing' ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>onNavigate('pricing'),\n                                className: `text-xs px-3 py-2 ${currentView === 'pricing' ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-300 hover:text-white'}`,\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/Header.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvSGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQXVDO0FBQ1M7QUFDcUM7QUFDekI7QUFDZjtBQUNOO0FBUXZDLE1BQU1VLFNBQVMsQ0FBQyxFQUFFQyxXQUFXLEVBQUVDLFVBQVUsRUFBRUMsVUFBVSxFQUFlO0lBQ2xFLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUdQLHlFQUFPQTtJQUN4QixxQkFDRSw4REFBQ1AsaURBQU1BLENBQUNlLE1BQU07UUFDWkMsU0FBUztZQUFFQyxTQUFTO1lBQUdDLEdBQUcsQ0FBQztRQUFHO1FBQzlCQyxTQUFTO1lBQUVGLFNBQVM7WUFBR0MsR0FBRztRQUFFO1FBQzVCRSxXQUFVO2tCQUVWLDRFQUFDQztZQUFJRCxXQUFVOzs4QkFDYiw4REFBQ0M7b0JBQUlELFdBQVU7O3NDQUViLDhEQUFDcEIsaURBQU1BLENBQUNxQixHQUFHOzRCQUNUQyxZQUFZO2dDQUFFQyxPQUFPOzRCQUFLOzRCQUMxQkgsV0FBVTs0QkFDVkksU0FBUyxJQUFNWixXQUFXOzs4Q0FFMUIsOERBQUNTO29DQUFJRCxXQUFVOzhDQUNiLDRFQUFDbEIsMkhBQVFBO3dDQUFDa0IsV0FBVTs7Ozs7Ozs7Ozs7OENBRXRCLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNLOzRDQUFHTCxXQUFVO3NEQUEwQzs7Ozs7O3NEQUN4RCw4REFBQ007NENBQUVOLFdBQVU7c0RBQXdDOzs7Ozs7Ozs7Ozs7OENBRXZELDhEQUFDQztvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQ0s7d0NBQUdMLFdBQVU7a0RBQStCOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLakQsOERBQUNPOzRCQUFJUCxXQUFVOzs4Q0FDYiw4REFBQ25CLHlEQUFNQTtvQ0FDTDJCLFNBQVNqQixnQkFBZ0IsWUFBWSxZQUFZO29DQUNqRGtCLE1BQUs7b0NBQ0xMLFNBQVMsSUFBTVosV0FBVztvQ0FDMUJRLFdBQVdULGdCQUFnQixZQUN2QixzQ0FDQTs7c0RBR0osOERBQUNQLHVIQUFJQTs0Q0FBQ2dCLFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozs7OENBSW5DLDhEQUFDbkIseURBQU1BO29DQUNMMkIsU0FBU2pCLGdCQUFnQixjQUFjLFlBQVk7b0NBQ25Ea0IsTUFBSztvQ0FDTEwsU0FBUyxJQUFNWixXQUFXO29DQUMxQlEsV0FBV1QsZ0JBQWdCLGNBQ3ZCLHNDQUNBOztzREFHSiw4REFBQ1QsMkhBQVFBOzRDQUFDa0IsV0FBVTs7Ozs7O3dDQUFpQjs7Ozs7Ozs4Q0FJdkMsOERBQUNuQix5REFBTUE7b0NBQ0wyQixTQUFTakIsZ0JBQWdCLFFBQVEsWUFBWTtvQ0FDN0NrQixNQUFLO29DQUNMTCxTQUFTLElBQU1aLFdBQVc7b0NBQzFCUSxXQUFXVCxnQkFBZ0IsUUFDdkIsc0NBQ0E7O3NEQUdKLDhEQUFDTiw2SEFBVUE7NENBQUNlLFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozs7OENBSXpDLDhEQUFDbkIseURBQU1BO29DQUNMMkIsU0FBU2pCLGdCQUFnQixZQUFZLFlBQVk7b0NBQ2pEa0IsTUFBSztvQ0FDTEwsU0FBUyxJQUFNWixXQUFXO29DQUMxQlEsV0FBV1QsZ0JBQWdCLFlBQ3ZCLHNDQUNBOztzREFHSiw4REFBQ0wsNkhBQVVBOzRDQUFDYyxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7Ozs7Ozs7O3NDQU0zQyw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDbkIseURBQU1BO29DQUNMMkIsU0FBUTtvQ0FDUkMsTUFBSztvQ0FDTEwsU0FBU1g7b0NBQ1RPLFdBQVU7O3NEQUVWLDhEQUFDakIsZ0lBQWFBOzRDQUFDaUIsV0FBVTs7Ozs7O3NEQUN6Qiw4REFBQ1U7NENBQUtWLFdBQVU7c0RBQW1COzs7Ozs7c0RBQ25DLDhEQUFDVTs0Q0FBS1YsV0FBVTtzREFBWTs7Ozs7Ozs7Ozs7O2dDQUc3Qk4scUJBQ0MsOERBQUNMLHNEQUFRQTtvQ0FBQ0csWUFBWUE7Ozs7OzhEQUV0Qiw4REFBQ0oseURBQVdBO29DQUNWb0IsU0FBUTtvQ0FDUkMsTUFBSztvQ0FDTFQsV0FBVTs7Ozs7OzhDQUtkLDhEQUFDQztvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQ25CLHlEQUFNQTt3Q0FDTDJCLFNBQVE7d0NBQ1JDLE1BQUs7d0NBQ0xMLFNBQVMsSUFBTVosV0FBVzt3Q0FDMUJRLFdBQVU7a0RBRVYsNEVBQUNsQiwySEFBUUE7NENBQUNrQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU81Qiw4REFBQ0M7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ25CLHlEQUFNQTtnQ0FDTDJCLFNBQVNqQixnQkFBZ0IsWUFBWSxZQUFZO2dDQUNqRGtCLE1BQUs7Z0NBQ0xMLFNBQVMsSUFBTVosV0FBVztnQ0FDMUJRLFdBQVcsQ0FBQyxrQkFBa0IsRUFBRVQsZ0JBQWdCLFlBQzVDLHNDQUNBLGtDQUNGOzBDQUNIOzs7Ozs7MENBSUQsOERBQUNWLHlEQUFNQTtnQ0FDTDJCLFNBQVNqQixnQkFBZ0IsY0FBYyxZQUFZO2dDQUNuRGtCLE1BQUs7Z0NBQ0xMLFNBQVMsSUFBTVosV0FBVztnQ0FDMUJRLFdBQVcsQ0FBQyxrQkFBa0IsRUFBRVQsZ0JBQWdCLGNBQzVDLHNDQUNBLGtDQUNGOzBDQUNIOzs7Ozs7MENBSUQsOERBQUNWLHlEQUFNQTtnQ0FDTDJCLFNBQVNqQixnQkFBZ0IsUUFBUSxZQUFZO2dDQUM3Q2tCLE1BQUs7Z0NBQ0xMLFNBQVMsSUFBTVosV0FBVztnQ0FDMUJRLFdBQVcsQ0FBQyxrQkFBa0IsRUFBRVQsZ0JBQWdCLFFBQzVDLHNDQUNBLGtDQUNGOzBDQUNIOzs7Ozs7MENBSUQsOERBQUNWLHlEQUFNQTtnQ0FDTDJCLFNBQVNqQixnQkFBZ0IsWUFBWSxZQUFZO2dDQUNqRGtCLE1BQUs7Z0NBQ0xMLFNBQVMsSUFBTVosV0FBVztnQ0FDMUJRLFdBQVcsQ0FBQyxrQkFBa0IsRUFBRVQsZ0JBQWdCLFlBQzVDLHNDQUNBLGtDQUNGOzBDQUNIOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWI7QUFFQSxpRUFBZUQsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2RpbmVzaHMvRG9jdW1lbnRzL0Rldi9Qcm9qZWN0cy9ZVFN1YnRpdGxlRXh0cmFjdG9yL1lUU3VidGl0bGVFeHRyYWN0b3IvY29tcG9uZW50cy9IZWFkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XG5pbXBvcnQgeyBEb3dubG9hZCwgTWVzc2FnZVNxdWFyZSwgSG9tZSwgSGVscENpcmNsZSwgQ3JlZGl0Q2FyZCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb21wb25lbnRzL2NvbnRleHRzL0F1dGhDb250ZXh0JztcbmltcG9ydCBMb2dpbkJ1dHRvbiBmcm9tICcuL2F1dGgvTG9naW5CdXR0b24nO1xuaW1wb3J0IFVzZXJNZW51IGZyb20gJy4vYXV0aC9Vc2VyTWVudSc7XG5cbmludGVyZmFjZSBIZWFkZXJQcm9wcyB7XG4gIGN1cnJlbnRWaWV3OiBzdHJpbmc7XG4gIG9uTmF2aWdhdGU6ICh2aWV3OiAnJyB8ICdleHRyYWN0b3InIHwgJ3Rlcm1zJyB8ICdwcml2YWN5JyB8ICdkaXNjbGFpbWVyJyB8ICdmYXEnIHwgJ3ByaWNpbmcnIHwgJ2Rhc2hib2FyZCcgfCAnc2V0dGluZ3MnKSA9PiB2b2lkO1xuICBvbkZlZWRiYWNrOiAoKSA9PiB2b2lkO1xufVxuXG5jb25zdCBIZWFkZXIgPSAoeyBjdXJyZW50Vmlldywgb25OYXZpZ2F0ZSwgb25GZWVkYmFjayB9OiBIZWFkZXJQcm9wcykgPT4ge1xuICBjb25zdCB7IHVzZXIgfSA9IHVzZUF1dGgoKTtcbiAgcmV0dXJuIChcbiAgICA8bW90aW9uLmhlYWRlclxuICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAtMjAgfX1cbiAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgY2xhc3NOYW1lPVwiYmctc2xhdGUtOTAwLzk1IGJhY2tkcm9wLWJsdXItc20gYm9yZGVyLWIgYm9yZGVyLXNsYXRlLTcwMCBzdGlja3kgdG9wLTAgei01MFwiXG4gICAgPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC0zIHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBoLTE0IHNtOmgtMTZcIj5cbiAgICAgICAgICB7LyogTG9nbyAqL31cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wNSB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHNtOnNwYWNlLXgtMyBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvbk5hdmlnYXRlKCcnKX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggc206dy0xMCBzbTpoLTEwIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNjAwIHRvLXBpbmstNjAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cInctNCBoLTQgc206dy02IHNtOmgtNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4geHM6YmxvY2tcIj5cbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtbGcgc206dGV4dC14bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPkRvd25sb2FkWVRTdWJ0aXRsZXM8L2gxPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgaGlkZGVuIHNtOmJsb2NrXCI+WW91VHViZSBTdWJ0aXRsZSBFeHRyYWN0b3I8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmxvY2sgeHM6aGlkZGVuXCI+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+RFlUUzwvaDE+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICB7LyogTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICA8bmF2IGNsYXNzTmFtZT1cImhpZGRlbiBtZDpmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTZcIj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgdmFyaWFudD17Y3VycmVudFZpZXcgPT09ICdsYW5kaW5nJyA/ICdkZWZhdWx0JyA6ICdnaG9zdCd9XG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uTmF2aWdhdGUoJycpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2N1cnJlbnRWaWV3ID09PSAnbGFuZGluZycgXG4gICAgICAgICAgICAgICAgPyAnYmctcHVycGxlLTYwMCBob3ZlcjpiZy1wdXJwbGUtNzAwJyBcbiAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtd2hpdGUnXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEhvbWUgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgSG9tZVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgdmFyaWFudD17Y3VycmVudFZpZXcgPT09ICdleHRyYWN0b3InID8gJ2RlZmF1bHQnIDogJ2dob3N0J31cbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb25OYXZpZ2F0ZSgnZXh0cmFjdG9yJyl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17Y3VycmVudFZpZXcgPT09ICdleHRyYWN0b3InIFxuICAgICAgICAgICAgICAgID8gJ2JnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCcgXG4gICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxEb3dubG9hZCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICBFeHRyYWN0XG4gICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PXtjdXJyZW50VmlldyA9PT0gJ2ZhcScgPyAnZGVmYXVsdCcgOiAnZ2hvc3QnfVxuICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvbk5hdmlnYXRlKCdmYXEnKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjdXJyZW50VmlldyA9PT0gJ2ZhcSdcbiAgICAgICAgICAgICAgICA/ICdiZy1wdXJwbGUtNjAwIGhvdmVyOmJnLXB1cnBsZS03MDAnXG4gICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxIZWxwQ2lyY2xlIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgIEZBUVxuICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgdmFyaWFudD17Y3VycmVudFZpZXcgPT09ICdwcmljaW5nJyA/ICdkZWZhdWx0JyA6ICdnaG9zdCd9XG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uTmF2aWdhdGUoJ3ByaWNpbmcnKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjdXJyZW50VmlldyA9PT0gJ3ByaWNpbmcnXG4gICAgICAgICAgICAgICAgPyAnYmctcHVycGxlLTYwMCBob3ZlcjpiZy1wdXJwbGUtNzAwJ1xuICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8Q3JlZGl0Q2FyZCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICBQcmljaW5nXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L25hdj5cblxuICAgICAgICAgIHsvKiBBY3Rpb25zICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHNtOnNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29uRmVlZGJhY2t9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1wdXJwbGUtNTAwIHRleHQtcHVycGxlLTQwMCBob3ZlcjpiZy1wdXJwbGUtNTAwIGhvdmVyOnRleHQtd2hpdGUgdGV4dC14cyBzbTp0ZXh0LXNtIHB4LTIgc206cHgtM1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxNZXNzYWdlU3F1YXJlIGNsYXNzTmFtZT1cInctMyBoLTMgc206dy00IHNtOmgtNCBtci0xIHNtOm1yLTJcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJoaWRkZW4geHM6aW5saW5lXCI+RmVlZGJhY2s8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInhzOmhpZGRlblwiPkZCPC9zcGFuPlxuICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgIHt1c2VyID8gKFxuICAgICAgICAgICAgICA8VXNlck1lbnUgb25OYXZpZ2F0ZT17b25OYXZpZ2F0ZX0gLz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxMb2dpbkJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1wdXJwbGUtNjAwIHRleHQtcHVycGxlLTQwMCBob3ZlcjpiZy1wdXJwbGUtNjAwIGhvdmVyOnRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIE1vYmlsZSBNZW51IEJ1dHRvbiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWQ6aGlkZGVuXCI+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb25OYXZpZ2F0ZSgnZXh0cmFjdG9yJyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlIHAtMlwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8RG93bmxvYWQgY2xhc3NOYW1lPVwidy00IGgtNCBzbTp3LTUgc206aC01XCIgLz5cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE1vYmlsZSBOYXZpZ2F0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1kOmhpZGRlbiBwYi0zXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTIgb3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9e2N1cnJlbnRWaWV3ID09PSAnbGFuZGluZycgPyAnZGVmYXVsdCcgOiAnZ2hvc3QnfVxuICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvbk5hdmlnYXRlKCcnKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC14cyBweC0zIHB5LTIgJHtjdXJyZW50VmlldyA9PT0gJ2xhbmRpbmcnXG4gICAgICAgICAgICAgICAgPyAnYmctcHVycGxlLTYwMCBob3ZlcjpiZy1wdXJwbGUtNzAwJ1xuICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIEhvbWVcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9e2N1cnJlbnRWaWV3ID09PSAnZXh0cmFjdG9yJyA/ICdkZWZhdWx0JyA6ICdnaG9zdCd9XG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uTmF2aWdhdGUoJ2V4dHJhY3RvcicpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXhzIHB4LTMgcHktMiAke2N1cnJlbnRWaWV3ID09PSAnZXh0cmFjdG9yJ1xuICAgICAgICAgICAgICAgID8gJ2JnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCdcbiAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtd2hpdGUnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBFeHRyYWN0XG4gICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PXtjdXJyZW50VmlldyA9PT0gJ2ZhcScgPyAnZGVmYXVsdCcgOiAnZ2hvc3QnfVxuICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvbk5hdmlnYXRlKCdmYXEnKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC14cyBweC0zIHB5LTIgJHtjdXJyZW50VmlldyA9PT0gJ2ZhcSdcbiAgICAgICAgICAgICAgICA/ICdiZy1wdXJwbGUtNjAwIGhvdmVyOmJnLXB1cnBsZS03MDAnXG4gICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgRkFRXG4gICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PXtjdXJyZW50VmlldyA9PT0gJ3ByaWNpbmcnID8gJ2RlZmF1bHQnIDogJ2dob3N0J31cbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb25OYXZpZ2F0ZSgncHJpY2luZycpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXhzIHB4LTMgcHktMiAke2N1cnJlbnRWaWV3ID09PSAncHJpY2luZydcbiAgICAgICAgICAgICAgICA/ICdiZy1wdXJwbGUtNjAwIGhvdmVyOmJnLXB1cnBsZS03MDAnXG4gICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgUHJpY2luZ1xuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9tb3Rpb24uaGVhZGVyPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgSGVhZGVyO1xuIl0sIm5hbWVzIjpbIm1vdGlvbiIsIkJ1dHRvbiIsIkRvd25sb2FkIiwiTWVzc2FnZVNxdWFyZSIsIkhvbWUiLCJIZWxwQ2lyY2xlIiwiQ3JlZGl0Q2FyZCIsInVzZUF1dGgiLCJMb2dpbkJ1dHRvbiIsIlVzZXJNZW51IiwiSGVhZGVyIiwiY3VycmVudFZpZXciLCJvbk5hdmlnYXRlIiwib25GZWVkYmFjayIsInVzZXIiLCJoZWFkZXIiLCJpbml0aWFsIiwib3BhY2l0eSIsInkiLCJhbmltYXRlIiwiY2xhc3NOYW1lIiwiZGl2Iiwid2hpbGVIb3ZlciIsInNjYWxlIiwib25DbGljayIsImgxIiwicCIsIm5hdiIsInZhcmlhbnQiLCJzaXplIiwic3BhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Header.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/auth/LoginButton.tsx":
/*!*****************************************!*\
  !*** ./components/auth/LoginButton.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-node)/./components/ui/button.tsx\");\n/* harmony import */ var _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/contexts/AuthContext */ \"(pages-dir-node)/./components/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,LogIn!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=Loader2,LogIn!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst LoginButton = ({ variant = 'default', size = 'default', className = '', children })=>{\n    const { signInWithGoogle, loading } = (0,_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n        onClick: signInWithGoogle,\n        disabled: loading,\n        variant: variant,\n        size: size,\n        className: className,\n        children: [\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Loader2, {\n                className: \"w-4 h-4 mr-2 animate-spin\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/LoginButton.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_4__.LogIn, {\n                className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/LoginButton.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, undefined),\n            children || 'Sign in with Google'\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/LoginButton.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoginButton);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/auth/LoginButton.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/auth/UserMenu.tsx":
/*!**************************************!*\
  !*** ./components/auth/UserMenu.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-node)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(pages-dir-node)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(pages-dir-node)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/contexts/AuthContext */ \"(pages-dir-node)/./components/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/hooks/useSubscription */ \"(pages-dir-node)/./components/hooks/useSubscription.ts\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Crown_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Crown,LogOut,Settings,User!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=CreditCard,Crown,LogOut,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__, _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__, _components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__, _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__, _components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst UserMenu = ({ onNavigate })=>{\n    const { user, signOut } = (0,_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { subscription } = (0,_components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__.useSubscription)();\n    if (!user) return null;\n    const getInitials = (name)=>{\n        return name.split(' ').map((word)=>word[0]).join('').toUpperCase().slice(0, 2);\n    };\n    const displayName = user.user_metadata?.full_name || user.user_metadata?.name || user.email?.split('@')[0] || 'User';\n    const avatarUrl = user.user_metadata?.avatar_url || user.user_metadata?.picture;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"ghost\",\n                    className: \"relative h-8 w-8 rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                        className: \"h-8 w-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                src: avatarUrl,\n                                alt: displayName\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                className: \"bg-purple-600 text-white\",\n                                children: getInitials(displayName)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                className: \"w-56\",\n                align: \"end\",\n                forceMount: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuLabel, {\n                        className: \"font-normal\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium leading-none\",\n                                    children: displayName\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs leading-none text-muted-foreground\",\n                                    children: user.email\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, undefined),\n                                subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Crown_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Crown, {\n                                            className: \"w-3 h-3 text-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-medium text-yellow-600 capitalize\",\n                                            children: [\n                                                subscription.tier,\n                                                \" Plan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>onNavigate('dashboard'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Crown_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__.User, {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>onNavigate('pricing'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Crown_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__.CreditCard, {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Subscription\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>onNavigate('settings'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Crown_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Settings, {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Settings\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: signOut,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Crown_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__.LogOut, {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Sign out\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/auth/UserMenu.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserMenu);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/auth/UserMenu.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/contexts/AuthContext.tsx":
/*!*********************************************!*\
  !*** ./components/contexts/AuthContext.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(pages-dir-node)/./lib/supabase.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_3__]);\nreact_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    try {\n                        const { data: { session }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                        if (error) {\n                            console.error('Error getting session:', error);\n                            setError(error.message);\n                        } else {\n                            setUser(session?.user || null);\n                        }\n                    } catch (err) {\n                        console.error('Error in getInitialSession:', err);\n                        setError('Failed to get session');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth changes\n            const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    console.log('Auth state changed:', event, session?.user?.email);\n                    if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {\n                        setUser(session?.user || null);\n                        setError(null);\n                        // Create or update user profile\n                        if (session?.user) {\n                            await createOrUpdateUserProfile(session.user);\n                        }\n                    } else if (event === 'SIGNED_OUT') {\n                        setUser(null);\n                        setError(null);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const createOrUpdateUserProfile = async (user)=>{\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('users').upsert({\n                id: user.id,\n                email: user.email || '',\n                full_name: user.user_metadata?.full_name || user.user_metadata?.name || null,\n                avatar_url: user.user_metadata?.avatar_url || user.user_metadata?.picture || null,\n                updated_at: new Date().toISOString()\n            }, {\n                onConflict: 'id'\n            });\n            if (error) {\n                console.error('Error creating/updating user profile:', error);\n            }\n        } catch (err) {\n            console.error('Error in createOrUpdateUserProfile:', err);\n        }\n    };\n    const signInWithGoogle = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOAuth({\n                provider: 'google',\n                options: {\n                    redirectTo: `${window.location.origin}/auth/callback`\n                }\n            });\n            if (error) {\n                setError(error.message);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Failed to sign in with Google');\n            }\n        } catch (err) {\n            console.error('Error signing in with Google:', err);\n            setError('Failed to sign in');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Failed to sign in with Google');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n            if (error) {\n                setError(error.message);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Failed to sign out');\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('Signed out successfully');\n            }\n        } catch (err) {\n            console.error('Error signing out:', err);\n            setError('Failed to sign out');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Failed to sign out');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const refreshUser = async ()=>{\n        try {\n            const { data: { user }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getUser();\n            if (error) {\n                setError(error.message);\n            } else {\n                setUser(user);\n            }\n        } catch (err) {\n            console.error('Error refreshing user:', err);\n            setError('Failed to refresh user');\n        }\n    };\n    const value = {\n        user,\n        loading,\n        error,\n        signInWithGoogle,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/contexts/AuthContext.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/hooks/useSubscription.ts":
/*!*********************************************!*\
  !*** ./components/hooks/useSubscription.ts ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSubscription: () => (/* binding */ useSubscription)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(pages-dir-node)/./lib/supabase.ts\");\n/* harmony import */ var _lib_stripe__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe */ \"(pages-dir-node)/./lib/stripe.ts\");\n/* harmony import */ var _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/contexts/AuthContext */ \"(pages-dir-node)/./components/contexts/AuthContext.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__]);\n([_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst useSubscription = ()=>{\n    const { user } = (0,_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [subscription, setSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [usage, setUsage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSubscription.useEffect\": ()=>{\n            if (user) {\n                fetchSubscriptionData();\n            } else {\n                setSubscription(null);\n                setUsage(null);\n                setLoading(false);\n            }\n        }\n    }[\"useSubscription.useEffect\"], [\n        user\n    ]);\n    const fetchSubscriptionData = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            setError(null);\n            // Fetch subscription\n            const { data: subscriptionData, error: subscriptionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('subscriptions').select('*').eq('user_id', user.id).eq('status', 'active').single();\n            if (subscriptionError && subscriptionError.code !== 'PGRST116') {\n                throw subscriptionError;\n            }\n            // Fetch usage stats\n            const { data: usageData, error: usageError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('usage_stats').select('*').eq('user_id', user.id).single();\n            if (usageError && usageError.code !== 'PGRST116') {\n                throw usageError;\n            }\n            setSubscription(subscriptionData);\n            setUsage(usageData);\n        } catch (err) {\n            console.error('Error fetching subscription data:', err);\n            setError('Failed to fetch subscription data');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const canPerformAction = (creditsNeeded = 1)=>{\n        if (!subscription || subscription.status !== 'active') {\n            return false;\n        }\n        const tier = _lib_stripe__WEBPACK_IMPORTED_MODULE_2__.PRICING_TIERS[subscription.tier];\n        if (!tier) return false;\n        // Check if user has enough credits\n        const currentCreditsUsed = usage?.credits_used_this_month || 0;\n        const availableCredits = tier.limits.creditsPerMonth - currentCreditsUsed;\n        return availableCredits >= creditsNeeded;\n    };\n    // Backward compatibility - check if user can extract a complete video (2 credits)\n    const canExtractVideo = ()=>{\n        return canPerformAction(2); // Get languages (1) + Download (1) = 2 credits\n    };\n    const getRemainingCredits = ()=>{\n        if (!subscription || subscription.status !== 'active') {\n            return 0;\n        }\n        const tier = _lib_stripe__WEBPACK_IMPORTED_MODULE_2__.PRICING_TIERS[subscription.tier];\n        if (!tier) return 0;\n        const currentCreditsUsed = usage?.credits_used_this_month || 0;\n        return Math.max(0, tier.limits.creditsPerMonth - currentCreditsUsed);\n    };\n    // Backward compatibility - get remaining complete extractions\n    const getRemainingExtractions = ()=>{\n        const remainingCredits = getRemainingCredits();\n        return Math.floor(remainingCredits / 2); // Each complete extraction needs 2 credits\n    };\n    // New: Get bandwidth cost information\n    const getBandwidthInfo = ()=>{\n        if (!subscription || subscription.status !== 'active') {\n            return null;\n        }\n        const tier = _lib_stripe__WEBPACK_IMPORTED_MODULE_2__.PRICING_TIERS[subscription.tier];\n        if (!tier) return null;\n        return {\n            bandwidthPerCredit: tier.limits.bandwidthPerCredit,\n            costPerCredit: tier.limits.costPerCredit,\n            estimatedBandwidthPerAction: `~${tier.limits.bandwidthPerCredit}MB`,\n            estimatedCostPerAction: `$${tier.limits.costPerCredit.toFixed(3)}`\n        };\n    };\n    // New: Check if user should be warned about credit usage\n    const shouldWarnAboutCredits = (creditsNeeded = 1)=>{\n        const remainingCredits = getRemainingCredits();\n        const warningThreshold = 10; // Warn when less than 10 credits remaining\n        return remainingCredits <= warningThreshold || remainingCredits < creditsNeeded;\n    };\n    // New: Get smart usage suggestions\n    const getUsageSuggestions = ()=>{\n        const remainingCredits = getRemainingCredits();\n        const suggestions = [];\n        if (remainingCredits <= 5) {\n            suggestions.push({\n                type: 'warning',\n                message: 'Low credits remaining. Consider upgrading your plan.',\n                action: 'upgrade'\n            });\n        }\n        if (remainingCredits <= 2) {\n            suggestions.push({\n                type: 'error',\n                message: 'Very low credits. Use language check before downloading to avoid waste.',\n                action: 'check-languages-first'\n            });\n        }\n        return suggestions;\n    };\n    const createCheckoutSession = async (tier)=>{\n        if (!user) {\n            throw new Error('User must be authenticated');\n        }\n        try {\n            const response = await fetch('/api/stripe/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    priceId: _lib_stripe__WEBPACK_IMPORTED_MODULE_2__.PRICING_TIERS[tier].priceId,\n                    userId: user.id,\n                    userEmail: user.email\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to create checkout session');\n            }\n            const { sessionId } = await response.json();\n            return sessionId;\n        } catch (err) {\n            console.error('Error creating checkout session:', err);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Failed to create checkout session');\n            throw err;\n        }\n    };\n    const cancelSubscription = async ()=>{\n        if (!subscription) {\n            throw new Error('No active subscription found');\n        }\n        try {\n            const response = await fetch('/api/user/subscription', {\n                method: 'DELETE',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    subscriptionId: subscription.stripe_subscription_id\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to cancel subscription');\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success('Subscription cancelled successfully');\n            await fetchSubscriptionData();\n        } catch (err) {\n            console.error('Error cancelling subscription:', err);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Failed to cancel subscription');\n            throw err;\n        }\n    };\n    const refreshSubscription = async ()=>{\n        await fetchSubscriptionData();\n    };\n    return {\n        subscription,\n        usage,\n        loading,\n        error,\n        canPerformAction,\n        canExtractVideo,\n        getRemainingCredits,\n        getRemainingExtractions,\n        getBandwidthInfo,\n        shouldWarnAboutCredits,\n        getUsageSuggestions,\n        createCheckoutSession,\n        cancelSubscription,\n        refreshSubscription\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvaG9va3MvdXNlU3Vic2NyaXB0aW9uLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBNEM7QUFDRjtBQUNxQjtBQUVIO0FBQ3hCO0FBRTdCLE1BQU1NLGtCQUFrQjtJQUM3QixNQUFNLEVBQUVDLElBQUksRUFBRSxHQUFHSCx5RUFBT0E7SUFDeEIsTUFBTSxDQUFDSSxjQUFjQyxnQkFBZ0IsR0FBR1QsK0NBQVFBLENBQXNCO0lBQ3RFLE1BQU0sQ0FBQ1UsT0FBT0MsU0FBUyxHQUFHWCwrQ0FBUUEsQ0FBb0I7SUFDdEQsTUFBTSxDQUFDWSxTQUFTQyxXQUFXLEdBQUdiLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ2MsT0FBT0MsU0FBUyxHQUFHZiwrQ0FBUUEsQ0FBZ0I7SUFFbERDLGdEQUFTQTtxQ0FBQztZQUNSLElBQUlNLE1BQU07Z0JBQ1JTO1lBQ0YsT0FBTztnQkFDTFAsZ0JBQWdCO2dCQUNoQkUsU0FBUztnQkFDVEUsV0FBVztZQUNiO1FBQ0Y7b0NBQUc7UUFBQ047S0FBSztJQUVULE1BQU1TLHdCQUF3QjtRQUM1QixJQUFJLENBQUNULE1BQU07UUFFWCxJQUFJO1lBQ0ZNLFdBQVc7WUFDWEUsU0FBUztZQUVULHFCQUFxQjtZQUNyQixNQUFNLEVBQUVFLE1BQU1DLGdCQUFnQixFQUFFSixPQUFPSyxpQkFBaUIsRUFBRSxHQUFHLE1BQU1qQixtREFBUUEsQ0FDeEVrQixJQUFJLENBQUMsaUJBQ0xDLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsV0FBV2YsS0FBS2dCLEVBQUUsRUFDckJELEVBQUUsQ0FBQyxVQUFVLFVBQ2JFLE1BQU07WUFFVCxJQUFJTCxxQkFBcUJBLGtCQUFrQk0sSUFBSSxLQUFLLFlBQVk7Z0JBQzlELE1BQU1OO1lBQ1I7WUFFQSxvQkFBb0I7WUFDcEIsTUFBTSxFQUFFRixNQUFNUyxTQUFTLEVBQUVaLE9BQU9hLFVBQVUsRUFBRSxHQUFHLE1BQU16QixtREFBUUEsQ0FDMURrQixJQUFJLENBQUMsZUFDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxXQUFXZixLQUFLZ0IsRUFBRSxFQUNyQkMsTUFBTTtZQUVULElBQUlHLGNBQWNBLFdBQVdGLElBQUksS0FBSyxZQUFZO2dCQUNoRCxNQUFNRTtZQUNSO1lBRUFsQixnQkFBZ0JTO1lBQ2hCUCxTQUFTZTtRQUNYLEVBQUUsT0FBT0UsS0FBSztZQUNaQyxRQUFRZixLQUFLLENBQUMscUNBQXFDYztZQUNuRGIsU0FBUztRQUNYLFNBQVU7WUFDUkYsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNaUIsbUJBQW1CLENBQUNDLGdCQUF3QixDQUFDO1FBQ2pELElBQUksQ0FBQ3ZCLGdCQUFnQkEsYUFBYXdCLE1BQU0sS0FBSyxVQUFVO1lBQ3JELE9BQU87UUFDVDtRQUVBLE1BQU1DLE9BQU85QixzREFBYSxDQUFDSyxhQUFheUIsSUFBSSxDQUFnQjtRQUM1RCxJQUFJLENBQUNBLE1BQU0sT0FBTztRQUVsQixtQ0FBbUM7UUFDbkMsTUFBTUMscUJBQXFCeEIsT0FBT3lCLDJCQUEyQjtRQUM3RCxNQUFNQyxtQkFBbUJILEtBQUtJLE1BQU0sQ0FBQ0MsZUFBZSxHQUFHSjtRQUV2RCxPQUFPRSxvQkFBb0JMO0lBQzdCO0lBRUEsa0ZBQWtGO0lBQ2xGLE1BQU1RLGtCQUFrQjtRQUN0QixPQUFPVCxpQkFBaUIsSUFBSSwrQ0FBK0M7SUFDN0U7SUFFQSxNQUFNVSxzQkFBc0I7UUFDMUIsSUFBSSxDQUFDaEMsZ0JBQWdCQSxhQUFhd0IsTUFBTSxLQUFLLFVBQVU7WUFDckQsT0FBTztRQUNUO1FBRUEsTUFBTUMsT0FBTzlCLHNEQUFhLENBQUNLLGFBQWF5QixJQUFJLENBQWdCO1FBQzVELElBQUksQ0FBQ0EsTUFBTSxPQUFPO1FBRWxCLE1BQU1DLHFCQUFxQnhCLE9BQU95QiwyQkFBMkI7UUFDN0QsT0FBT00sS0FBS0MsR0FBRyxDQUFDLEdBQUdULEtBQUtJLE1BQU0sQ0FBQ0MsZUFBZSxHQUFHSjtJQUNuRDtJQUVBLDhEQUE4RDtJQUM5RCxNQUFNUywwQkFBMEI7UUFDOUIsTUFBTUMsbUJBQW1CSjtRQUN6QixPQUFPQyxLQUFLSSxLQUFLLENBQUNELG1CQUFtQixJQUFJLDJDQUEyQztJQUN0RjtJQUVBLHNDQUFzQztJQUN0QyxNQUFNRSxtQkFBbUI7UUFDdkIsSUFBSSxDQUFDdEMsZ0JBQWdCQSxhQUFhd0IsTUFBTSxLQUFLLFVBQVU7WUFDckQsT0FBTztRQUNUO1FBRUEsTUFBTUMsT0FBTzlCLHNEQUFhLENBQUNLLGFBQWF5QixJQUFJLENBQWdCO1FBQzVELElBQUksQ0FBQ0EsTUFBTSxPQUFPO1FBRWxCLE9BQU87WUFDTGMsb0JBQW9CZCxLQUFLSSxNQUFNLENBQUNVLGtCQUFrQjtZQUNsREMsZUFBZWYsS0FBS0ksTUFBTSxDQUFDVyxhQUFhO1lBQ3hDQyw2QkFBNkIsQ0FBQyxDQUFDLEVBQUVoQixLQUFLSSxNQUFNLENBQUNVLGtCQUFrQixDQUFDLEVBQUUsQ0FBQztZQUNuRUcsd0JBQXdCLENBQUMsQ0FBQyxFQUFFakIsS0FBS0ksTUFBTSxDQUFDVyxhQUFhLENBQUNHLE9BQU8sQ0FBQyxJQUFJO1FBQ3BFO0lBQ0Y7SUFFQSx5REFBeUQ7SUFDekQsTUFBTUMseUJBQXlCLENBQUNyQixnQkFBd0IsQ0FBQztRQUN2RCxNQUFNYSxtQkFBbUJKO1FBQ3pCLE1BQU1hLG1CQUFtQixJQUFJLDJDQUEyQztRQUV4RSxPQUFPVCxvQkFBb0JTLG9CQUFvQlQsbUJBQW1CYjtJQUNwRTtJQUVBLG1DQUFtQztJQUNuQyxNQUFNdUIsc0JBQXNCO1FBQzFCLE1BQU1WLG1CQUFtQko7UUFDekIsTUFBTWUsY0FBYyxFQUFFO1FBRXRCLElBQUlYLG9CQUFvQixHQUFHO1lBQ3pCVyxZQUFZQyxJQUFJLENBQUM7Z0JBQ2ZDLE1BQU07Z0JBQ05DLFNBQVM7Z0JBQ1RDLFFBQVE7WUFDVjtRQUNGO1FBRUEsSUFBSWYsb0JBQW9CLEdBQUc7WUFDekJXLFlBQVlDLElBQUksQ0FBQztnQkFDZkMsTUFBTTtnQkFDTkMsU0FBUztnQkFDVEMsUUFBUTtZQUNWO1FBQ0Y7UUFFQSxPQUFPSjtJQUNUO0lBRUEsTUFBTUssd0JBQXdCLE9BQU8zQjtRQUNuQyxJQUFJLENBQUMxQixNQUFNO1lBQ1QsTUFBTSxJQUFJc0QsTUFBTTtRQUNsQjtRQUVBLElBQUk7WUFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU0sd0JBQXdCO2dCQUNuREMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CQyxTQUFTbEUsc0RBQWEsQ0FBQzhCLEtBQUssQ0FBQ29DLE9BQU87b0JBQ3BDQyxRQUFRL0QsS0FBS2dCLEVBQUU7b0JBQ2ZnRCxXQUFXaEUsS0FBS2lFLEtBQUs7Z0JBQ3ZCO1lBQ0Y7WUFFQSxJQUFJLENBQUNWLFNBQVNXLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJWixNQUFNO1lBQ2xCO1lBRUEsTUFBTSxFQUFFYSxTQUFTLEVBQUUsR0FBRyxNQUFNWixTQUFTYSxJQUFJO1lBQ3pDLE9BQU9EO1FBQ1QsRUFBRSxPQUFPOUMsS0FBSztZQUNaQyxRQUFRZixLQUFLLENBQUMsb0NBQW9DYztZQUNsRHZCLDZEQUFXLENBQUM7WUFDWixNQUFNdUI7UUFDUjtJQUNGO0lBRUEsTUFBTWdELHFCQUFxQjtRQUN6QixJQUFJLENBQUNwRSxjQUFjO1lBQ2pCLE1BQU0sSUFBSXFELE1BQU07UUFDbEI7UUFFQSxJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLDBCQUEwQjtnQkFDckRDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQlMsZ0JBQWdCckUsYUFBYXNFLHNCQUFzQjtnQkFDckQ7WUFDRjtZQUVBLElBQUksQ0FBQ2hCLFNBQVNXLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJWixNQUFNO1lBQ2xCO1lBRUF4RCwrREFBYSxDQUFDO1lBQ2QsTUFBTVc7UUFDUixFQUFFLE9BQU9ZLEtBQUs7WUFDWkMsUUFBUWYsS0FBSyxDQUFDLGtDQUFrQ2M7WUFDaER2Qiw2REFBVyxDQUFDO1lBQ1osTUFBTXVCO1FBQ1I7SUFDRjtJQUVBLE1BQU1vRCxzQkFBc0I7UUFDMUIsTUFBTWhFO0lBQ1I7SUFFQSxPQUFPO1FBQ0xSO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBQ0FnQjtRQUNBUztRQUNBQztRQUNBRztRQUNBRztRQUNBTTtRQUNBRTtRQUNBTTtRQUNBZ0I7UUFDQUk7SUFDRjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kaW5lc2hzL0RvY3VtZW50cy9EZXYvUHJvamVjdHMvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ZVFN1YnRpdGxlRXh0cmFjdG9yL2NvbXBvbmVudHMvaG9va3MvdXNlU3Vic2NyaXB0aW9uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBzdXBhYmFzZSB9IGZyb20gJ0AvbGliL3N1cGFiYXNlJztcbmltcG9ydCB7IFBSSUNJTkdfVElFUlMsIHR5cGUgUHJpY2luZ1RpZXIgfSBmcm9tICdAL2xpYi9zdHJpcGUnO1xuaW1wb3J0IHR5cGUgeyBTdWJzY3JpcHRpb24sIFVzYWdlU3RhdHMgfSBmcm9tICdAL2xpYi90eXBlcy9zdWJzY3JpcHRpb24nO1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29tcG9uZW50cy9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgdG9hc3QgZnJvbSAncmVhY3QtaG90LXRvYXN0JztcblxuZXhwb3J0IGNvbnN0IHVzZVN1YnNjcmlwdGlvbiA9ICgpID0+IHtcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IFtzdWJzY3JpcHRpb24sIHNldFN1YnNjcmlwdGlvbl0gPSB1c2VTdGF0ZTxTdWJzY3JpcHRpb24gfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3VzYWdlLCBzZXRVc2FnZV0gPSB1c2VTdGF0ZTxVc2FnZVN0YXRzIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHVzZXIpIHtcbiAgICAgIGZldGNoU3Vic2NyaXB0aW9uRGF0YSgpO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRTdWJzY3JpcHRpb24obnVsbCk7XG4gICAgICBzZXRVc2FnZShudWxsKTtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfSwgW3VzZXJdKTtcblxuICBjb25zdCBmZXRjaFN1YnNjcmlwdGlvbkRhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF1c2VyKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgICAvLyBGZXRjaCBzdWJzY3JpcHRpb25cbiAgICAgIGNvbnN0IHsgZGF0YTogc3Vic2NyaXB0aW9uRGF0YSwgZXJyb3I6IHN1YnNjcmlwdGlvbkVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnc3Vic2NyaXB0aW9ucycpXG4gICAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgICAgICAuZXEoJ3N0YXR1cycsICdhY3RpdmUnKVxuICAgICAgICAuc2luZ2xlKCk7XG5cbiAgICAgIGlmIChzdWJzY3JpcHRpb25FcnJvciAmJiBzdWJzY3JpcHRpb25FcnJvci5jb2RlICE9PSAnUEdSU1QxMTYnKSB7XG4gICAgICAgIHRocm93IHN1YnNjcmlwdGlvbkVycm9yO1xuICAgICAgfVxuXG4gICAgICAvLyBGZXRjaCB1c2FnZSBzdGF0c1xuICAgICAgY29uc3QgeyBkYXRhOiB1c2FnZURhdGEsIGVycm9yOiB1c2FnZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgndXNhZ2Vfc3RhdHMnKVxuICAgICAgICAuc2VsZWN0KCcqJylcbiAgICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgICAgLnNpbmdsZSgpO1xuXG4gICAgICBpZiAodXNhZ2VFcnJvciAmJiB1c2FnZUVycm9yLmNvZGUgIT09ICdQR1JTVDExNicpIHtcbiAgICAgICAgdGhyb3cgdXNhZ2VFcnJvcjtcbiAgICAgIH1cblxuICAgICAgc2V0U3Vic2NyaXB0aW9uKHN1YnNjcmlwdGlvbkRhdGEgYXMgU3Vic2NyaXB0aW9uKTtcbiAgICAgIHNldFVzYWdlKHVzYWdlRGF0YSBhcyBVc2FnZVN0YXRzKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHN1YnNjcmlwdGlvbiBkYXRhOicsIGVycik7XG4gICAgICBzZXRFcnJvcignRmFpbGVkIHRvIGZldGNoIHN1YnNjcmlwdGlvbiBkYXRhJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBjYW5QZXJmb3JtQWN0aW9uID0gKGNyZWRpdHNOZWVkZWQ6IG51bWJlciA9IDEpOiBib29sZWFuID0+IHtcbiAgICBpZiAoIXN1YnNjcmlwdGlvbiB8fCBzdWJzY3JpcHRpb24uc3RhdHVzICE9PSAnYWN0aXZlJykge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cblxuICAgIGNvbnN0IHRpZXIgPSBQUklDSU5HX1RJRVJTW3N1YnNjcmlwdGlvbi50aWVyIGFzIFByaWNpbmdUaWVyXTtcbiAgICBpZiAoIXRpZXIpIHJldHVybiBmYWxzZTtcblxuICAgIC8vIENoZWNrIGlmIHVzZXIgaGFzIGVub3VnaCBjcmVkaXRzXG4gICAgY29uc3QgY3VycmVudENyZWRpdHNVc2VkID0gdXNhZ2U/LmNyZWRpdHNfdXNlZF90aGlzX21vbnRoIHx8IDA7XG4gICAgY29uc3QgYXZhaWxhYmxlQ3JlZGl0cyA9IHRpZXIubGltaXRzLmNyZWRpdHNQZXJNb250aCAtIGN1cnJlbnRDcmVkaXRzVXNlZDtcblxuICAgIHJldHVybiBhdmFpbGFibGVDcmVkaXRzID49IGNyZWRpdHNOZWVkZWQ7XG4gIH07XG5cbiAgLy8gQmFja3dhcmQgY29tcGF0aWJpbGl0eSAtIGNoZWNrIGlmIHVzZXIgY2FuIGV4dHJhY3QgYSBjb21wbGV0ZSB2aWRlbyAoMiBjcmVkaXRzKVxuICBjb25zdCBjYW5FeHRyYWN0VmlkZW8gPSAoKTogYm9vbGVhbiA9PiB7XG4gICAgcmV0dXJuIGNhblBlcmZvcm1BY3Rpb24oMik7IC8vIEdldCBsYW5ndWFnZXMgKDEpICsgRG93bmxvYWQgKDEpID0gMiBjcmVkaXRzXG4gIH07XG5cbiAgY29uc3QgZ2V0UmVtYWluaW5nQ3JlZGl0cyA9ICgpOiBudW1iZXIgPT4ge1xuICAgIGlmICghc3Vic2NyaXB0aW9uIHx8IHN1YnNjcmlwdGlvbi5zdGF0dXMgIT09ICdhY3RpdmUnKSB7XG4gICAgICByZXR1cm4gMDtcbiAgICB9XG5cbiAgICBjb25zdCB0aWVyID0gUFJJQ0lOR19USUVSU1tzdWJzY3JpcHRpb24udGllciBhcyBQcmljaW5nVGllcl07XG4gICAgaWYgKCF0aWVyKSByZXR1cm4gMDtcblxuICAgIGNvbnN0IGN1cnJlbnRDcmVkaXRzVXNlZCA9IHVzYWdlPy5jcmVkaXRzX3VzZWRfdGhpc19tb250aCB8fCAwO1xuICAgIHJldHVybiBNYXRoLm1heCgwLCB0aWVyLmxpbWl0cy5jcmVkaXRzUGVyTW9udGggLSBjdXJyZW50Q3JlZGl0c1VzZWQpO1xuICB9O1xuXG4gIC8vIEJhY2t3YXJkIGNvbXBhdGliaWxpdHkgLSBnZXQgcmVtYWluaW5nIGNvbXBsZXRlIGV4dHJhY3Rpb25zXG4gIGNvbnN0IGdldFJlbWFpbmluZ0V4dHJhY3Rpb25zID0gKCk6IG51bWJlciA9PiB7XG4gICAgY29uc3QgcmVtYWluaW5nQ3JlZGl0cyA9IGdldFJlbWFpbmluZ0NyZWRpdHMoKTtcbiAgICByZXR1cm4gTWF0aC5mbG9vcihyZW1haW5pbmdDcmVkaXRzIC8gMik7IC8vIEVhY2ggY29tcGxldGUgZXh0cmFjdGlvbiBuZWVkcyAyIGNyZWRpdHNcbiAgfTtcblxuICAvLyBOZXc6IEdldCBiYW5kd2lkdGggY29zdCBpbmZvcm1hdGlvblxuICBjb25zdCBnZXRCYW5kd2lkdGhJbmZvID0gKCkgPT4ge1xuICAgIGlmICghc3Vic2NyaXB0aW9uIHx8IHN1YnNjcmlwdGlvbi5zdGF0dXMgIT09ICdhY3RpdmUnKSB7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICBjb25zdCB0aWVyID0gUFJJQ0lOR19USUVSU1tzdWJzY3JpcHRpb24udGllciBhcyBQcmljaW5nVGllcl07XG4gICAgaWYgKCF0aWVyKSByZXR1cm4gbnVsbDtcblxuICAgIHJldHVybiB7XG4gICAgICBiYW5kd2lkdGhQZXJDcmVkaXQ6IHRpZXIubGltaXRzLmJhbmR3aWR0aFBlckNyZWRpdCxcbiAgICAgIGNvc3RQZXJDcmVkaXQ6IHRpZXIubGltaXRzLmNvc3RQZXJDcmVkaXQsXG4gICAgICBlc3RpbWF0ZWRCYW5kd2lkdGhQZXJBY3Rpb246IGB+JHt0aWVyLmxpbWl0cy5iYW5kd2lkdGhQZXJDcmVkaXR9TUJgLFxuICAgICAgZXN0aW1hdGVkQ29zdFBlckFjdGlvbjogYCQke3RpZXIubGltaXRzLmNvc3RQZXJDcmVkaXQudG9GaXhlZCgzKX1gXG4gICAgfTtcbiAgfTtcblxuICAvLyBOZXc6IENoZWNrIGlmIHVzZXIgc2hvdWxkIGJlIHdhcm5lZCBhYm91dCBjcmVkaXQgdXNhZ2VcbiAgY29uc3Qgc2hvdWxkV2FybkFib3V0Q3JlZGl0cyA9IChjcmVkaXRzTmVlZGVkOiBudW1iZXIgPSAxKTogYm9vbGVhbiA9PiB7XG4gICAgY29uc3QgcmVtYWluaW5nQ3JlZGl0cyA9IGdldFJlbWFpbmluZ0NyZWRpdHMoKTtcbiAgICBjb25zdCB3YXJuaW5nVGhyZXNob2xkID0gMTA7IC8vIFdhcm4gd2hlbiBsZXNzIHRoYW4gMTAgY3JlZGl0cyByZW1haW5pbmdcblxuICAgIHJldHVybiByZW1haW5pbmdDcmVkaXRzIDw9IHdhcm5pbmdUaHJlc2hvbGQgfHwgcmVtYWluaW5nQ3JlZGl0cyA8IGNyZWRpdHNOZWVkZWQ7XG4gIH07XG5cbiAgLy8gTmV3OiBHZXQgc21hcnQgdXNhZ2Ugc3VnZ2VzdGlvbnNcbiAgY29uc3QgZ2V0VXNhZ2VTdWdnZXN0aW9ucyA9ICgpID0+IHtcbiAgICBjb25zdCByZW1haW5pbmdDcmVkaXRzID0gZ2V0UmVtYWluaW5nQ3JlZGl0cygpO1xuICAgIGNvbnN0IHN1Z2dlc3Rpb25zID0gW107XG5cbiAgICBpZiAocmVtYWluaW5nQ3JlZGl0cyA8PSA1KSB7XG4gICAgICBzdWdnZXN0aW9ucy5wdXNoKHtcbiAgICAgICAgdHlwZTogJ3dhcm5pbmcnLFxuICAgICAgICBtZXNzYWdlOiAnTG93IGNyZWRpdHMgcmVtYWluaW5nLiBDb25zaWRlciB1cGdyYWRpbmcgeW91ciBwbGFuLicsXG4gICAgICAgIGFjdGlvbjogJ3VwZ3JhZGUnXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICBpZiAocmVtYWluaW5nQ3JlZGl0cyA8PSAyKSB7XG4gICAgICBzdWdnZXN0aW9ucy5wdXNoKHtcbiAgICAgICAgdHlwZTogJ2Vycm9yJyxcbiAgICAgICAgbWVzc2FnZTogJ1ZlcnkgbG93IGNyZWRpdHMuIFVzZSBsYW5ndWFnZSBjaGVjayBiZWZvcmUgZG93bmxvYWRpbmcgdG8gYXZvaWQgd2FzdGUuJyxcbiAgICAgICAgYWN0aW9uOiAnY2hlY2stbGFuZ3VhZ2VzLWZpcnN0J1xuICAgICAgfSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHN1Z2dlc3Rpb25zO1xuICB9O1xuXG4gIGNvbnN0IGNyZWF0ZUNoZWNrb3V0U2Vzc2lvbiA9IGFzeW5jICh0aWVyOiBQcmljaW5nVGllcik6IFByb21pc2U8c3RyaW5nPiA9PiB7XG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbXVzdCBiZSBhdXRoZW50aWNhdGVkJyk7XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvc3RyaXBlL2NoZWNrb3V0Jywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBwcmljZUlkOiBQUklDSU5HX1RJRVJTW3RpZXJdLnByaWNlSWQsXG4gICAgICAgICAgdXNlcklkOiB1c2VyLmlkLFxuICAgICAgICAgIHVzZXJFbWFpbDogdXNlci5lbWFpbFxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGNyZWF0ZSBjaGVja291dCBzZXNzaW9uJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHsgc2Vzc2lvbklkIH0gPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICByZXR1cm4gc2Vzc2lvbklkO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgY2hlY2tvdXQgc2Vzc2lvbjonLCBlcnIpO1xuICAgICAgdG9hc3QuZXJyb3IoJ0ZhaWxlZCB0byBjcmVhdGUgY2hlY2tvdXQgc2Vzc2lvbicpO1xuICAgICAgdGhyb3cgZXJyO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBjYW5jZWxTdWJzY3JpcHRpb24gPSBhc3luYyAoKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgaWYgKCFzdWJzY3JpcHRpb24pIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTm8gYWN0aXZlIHN1YnNjcmlwdGlvbiBmb3VuZCcpO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3VzZXIvc3Vic2NyaXB0aW9uJywge1xuICAgICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIHN1YnNjcmlwdGlvbklkOiBzdWJzY3JpcHRpb24uc3RyaXBlX3N1YnNjcmlwdGlvbl9pZFxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGNhbmNlbCBzdWJzY3JpcHRpb24nKTtcbiAgICAgIH1cblxuICAgICAgdG9hc3Quc3VjY2VzcygnU3Vic2NyaXB0aW9uIGNhbmNlbGxlZCBzdWNjZXNzZnVsbHknKTtcbiAgICAgIGF3YWl0IGZldGNoU3Vic2NyaXB0aW9uRGF0YSgpO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2FuY2VsbGluZyBzdWJzY3JpcHRpb246JywgZXJyKTtcbiAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gY2FuY2VsIHN1YnNjcmlwdGlvbicpO1xuICAgICAgdGhyb3cgZXJyO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCByZWZyZXNoU3Vic2NyaXB0aW9uID0gYXN5bmMgKCk6IFByb21pc2U8dm9pZD4gPT4ge1xuICAgIGF3YWl0IGZldGNoU3Vic2NyaXB0aW9uRGF0YSgpO1xuICB9O1xuXG4gIHJldHVybiB7XG4gICAgc3Vic2NyaXB0aW9uLFxuICAgIHVzYWdlLFxuICAgIGxvYWRpbmcsXG4gICAgZXJyb3IsXG4gICAgY2FuUGVyZm9ybUFjdGlvbixcbiAgICBjYW5FeHRyYWN0VmlkZW8sXG4gICAgZ2V0UmVtYWluaW5nQ3JlZGl0cyxcbiAgICBnZXRSZW1haW5pbmdFeHRyYWN0aW9ucyxcbiAgICBnZXRCYW5kd2lkdGhJbmZvLFxuICAgIHNob3VsZFdhcm5BYm91dENyZWRpdHMsXG4gICAgZ2V0VXNhZ2VTdWdnZXN0aW9ucyxcbiAgICBjcmVhdGVDaGVja291dFNlc3Npb24sXG4gICAgY2FuY2VsU3Vic2NyaXB0aW9uLFxuICAgIHJlZnJlc2hTdWJzY3JpcHRpb25cbiAgfTtcbn07XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJzdXBhYmFzZSIsIlBSSUNJTkdfVElFUlMiLCJ1c2VBdXRoIiwidG9hc3QiLCJ1c2VTdWJzY3JpcHRpb24iLCJ1c2VyIiwic3Vic2NyaXB0aW9uIiwic2V0U3Vic2NyaXB0aW9uIiwidXNhZ2UiLCJzZXRVc2FnZSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImZldGNoU3Vic2NyaXB0aW9uRGF0YSIsImRhdGEiLCJzdWJzY3JpcHRpb25EYXRhIiwic3Vic2NyaXB0aW9uRXJyb3IiLCJmcm9tIiwic2VsZWN0IiwiZXEiLCJpZCIsInNpbmdsZSIsImNvZGUiLCJ1c2FnZURhdGEiLCJ1c2FnZUVycm9yIiwiZXJyIiwiY29uc29sZSIsImNhblBlcmZvcm1BY3Rpb24iLCJjcmVkaXRzTmVlZGVkIiwic3RhdHVzIiwidGllciIsImN1cnJlbnRDcmVkaXRzVXNlZCIsImNyZWRpdHNfdXNlZF90aGlzX21vbnRoIiwiYXZhaWxhYmxlQ3JlZGl0cyIsImxpbWl0cyIsImNyZWRpdHNQZXJNb250aCIsImNhbkV4dHJhY3RWaWRlbyIsImdldFJlbWFpbmluZ0NyZWRpdHMiLCJNYXRoIiwibWF4IiwiZ2V0UmVtYWluaW5nRXh0cmFjdGlvbnMiLCJyZW1haW5pbmdDcmVkaXRzIiwiZmxvb3IiLCJnZXRCYW5kd2lkdGhJbmZvIiwiYmFuZHdpZHRoUGVyQ3JlZGl0IiwiY29zdFBlckNyZWRpdCIsImVzdGltYXRlZEJhbmR3aWR0aFBlckFjdGlvbiIsImVzdGltYXRlZENvc3RQZXJBY3Rpb24iLCJ0b0ZpeGVkIiwic2hvdWxkV2FybkFib3V0Q3JlZGl0cyIsIndhcm5pbmdUaHJlc2hvbGQiLCJnZXRVc2FnZVN1Z2dlc3Rpb25zIiwic3VnZ2VzdGlvbnMiLCJwdXNoIiwidHlwZSIsIm1lc3NhZ2UiLCJhY3Rpb24iLCJjcmVhdGVDaGVja291dFNlc3Npb24iLCJFcnJvciIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJwcmljZUlkIiwidXNlcklkIiwidXNlckVtYWlsIiwiZW1haWwiLCJvayIsInNlc3Npb25JZCIsImpzb24iLCJjYW5jZWxTdWJzY3JpcHRpb24iLCJzdWJzY3JpcHRpb25JZCIsInN0cmlwZV9zdWJzY3JpcHRpb25faWQiLCJzdWNjZXNzIiwicmVmcmVzaFN1YnNjcmlwdGlvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/hooks/useSubscription.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./components/pricing/PricingCard.tsx":
/*!********************************************!*\
  !*** ./components/pricing/PricingCard.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-node)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(pages-dir-node)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(pages-dir-node)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Crown,Star,Zap!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=Check,Crown,Star,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_stripe__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/stripe */ \"(pages-dir-node)/./lib/stripe.ts\");\n/* harmony import */ var _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/contexts/AuthContext */ \"(pages-dir-node)/./components/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/hooks/useSubscription */ \"(pages-dir-node)/./components/hooks/useSubscription.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__, _components_ui_button__WEBPACK_IMPORTED_MODULE_3__, _components_ui_card__WEBPACK_IMPORTED_MODULE_4__, _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__, _components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_8__, react_hot_toast__WEBPACK_IMPORTED_MODULE_9__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_2__, _components_ui_button__WEBPACK_IMPORTED_MODULE_3__, _components_ui_card__WEBPACK_IMPORTED_MODULE_4__, _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__, _components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_8__, react_hot_toast__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\nconst PricingCard = ({ tier, isCurrentPlan = false, onSelectPlan })=>{\n    const { user } = (0,_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const { createCheckoutSession } = (0,_components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_8__.useSubscription)();\n    const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const tierData = _lib_stripe__WEBPACK_IMPORTED_MODULE_6__.PRICING_TIERS[tier];\n    const handleSelectPlan = async ()=>{\n        if (!user) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error('Please sign in to subscribe');\n            return;\n        }\n        if (isCurrentPlan) {\n            (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"])('This is your current plan');\n            return;\n        }\n        try {\n            setLoading(true);\n            const sessionId = await createCheckoutSession(tier);\n            const stripe = await _lib_stripe__WEBPACK_IMPORTED_MODULE_6__.stripePromise;\n            if (!stripe) {\n                throw new Error('Stripe failed to load');\n            }\n            const { error } = await stripe.redirectToCheckout({\n                sessionId\n            });\n            if (error) {\n                throw error;\n            }\n        } catch (err) {\n            console.error('Error selecting plan:', err);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error('Failed to start checkout process');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getIcon = ()=>{\n        switch(tier){\n            case 'starter':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Zap, {\n                    className: \"w-6 h-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 16\n                }, undefined);\n            case 'pro':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Star, {\n                    className: \"w-6 h-6 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 16\n                }, undefined);\n            case 'creator':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Crown, {\n                    className: \"w-6 h-6 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 16\n                }, undefined);\n            case 'enterprise':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Crown, {\n                    className: \"w-6 h-6 text-gold-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Zap, {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getGradient = ()=>{\n        switch(tier){\n            case 'starter':\n                return 'from-blue-500 to-cyan-500';\n            case 'pro':\n                return 'from-purple-500 to-pink-500';\n            case 'creator':\n                return 'from-yellow-500 to-orange-500';\n            case 'enterprise':\n                return 'from-amber-500 to-yellow-500';\n            default:\n                return 'from-gray-500 to-gray-600';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.5\n        },\n        className: \"relative\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: `relative overflow-hidden ${tierData.popular ? 'border-purple-500 shadow-lg shadow-purple-500/20' : 'border-slate-700'} bg-slate-800/80 backdrop-blur-sm`,\n            children: [\n                tierData.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-0 left-0 right-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-purple-500 to-pink-500 text-white text-center py-2 text-sm font-medium\",\n                        children: \"Most Popular\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    className: tierData.popular ? 'pt-12' : 'pt-6',\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        getIcon(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-white\",\n                                            children: tierData.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, undefined),\n                                isCurrentPlan && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"bg-green-600 text-white\",\n                                    children: \"Current Plan\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                            className: \"text-gray-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-baseline gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-3xl font-bold text-white\",\n                                        children: [\n                                            \"$\",\n                                            tierData.price\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400\",\n                                        children: \"/month\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"space-y-3\",\n                            children: tierData.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"flex items-start gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Check, {\n                                            className: \"w-5 h-5 text-green-500 mt-0.5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: feature\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4 border-t border-slate-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-white mb-2\",\n                                    children: \"High-Bandwidth Proxy Model\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-sm text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Monthly credits:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: tierData.limits.creditsPerMonth.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Total bandwidth:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-300 font-medium\",\n                                                    children: [\n                                                        \"~\",\n                                                        tierData.limits.creditsPerMonth * (tierData.limits.bandwidthPerCredit || 20),\n                                                        \"MB\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs space-y-1 bg-slate-700/30 rounded p-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Get languages:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"1 credit (~20MB)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Download subtitles:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"1 credit (~20MB)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between border-t border-slate-600 pt-1 font-medium text-gray-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Complete extraction:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"2 credits (~40MB)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 text-center space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        \"~\",\n                                                        Math.floor(tierData.limits.creditsPerMonth / tierData.limits.actionsPerExtraction),\n                                                        \" complete extractions/month\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-400\",\n                                                    children: \"Credits valid for 6 months\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleSelectPlan,\n                        disabled: loading || isCurrentPlan,\n                        className: `w-full ${tierData.popular ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`,\n                        children: loading ? 'Processing...' : isCurrentPlan ? 'Current Plan' : `Choose ${tierData.name}`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `absolute inset-0 bg-gradient-to-br ${getGradient()} opacity-5 pointer-events-none`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingCard.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PricingCard);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/pricing/PricingCard.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/pricing/PricingPage.tsx":
/*!********************************************!*\
  !*** ./components/pricing/PricingPage.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _PricingCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PricingCard */ \"(pages-dir-node)/./components/pricing/PricingCard.tsx\");\n/* harmony import */ var _lib_stripe__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/stripe */ \"(pages-dir-node)/./lib/stripe.ts\");\n/* harmony import */ var _components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/hooks/useSubscription */ \"(pages-dir-node)/./components/hooks/useSubscription.ts\");\n/* harmony import */ var _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/contexts/AuthContext */ \"(pages-dir-node)/./components/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth_LoginButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth/LoginButton */ \"(pages-dir-node)/./components/auth/LoginButton.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=ArrowLeft!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-node)/./components/ui/button.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__, _PricingCard__WEBPACK_IMPORTED_MODULE_3__, _components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__, _components_auth_LoginButton__WEBPACK_IMPORTED_MODULE_7__, _components_ui_button__WEBPACK_IMPORTED_MODULE_8__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_2__, _PricingCard__WEBPACK_IMPORTED_MODULE_3__, _components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__, _components_auth_LoginButton__WEBPACK_IMPORTED_MODULE_7__, _components_ui_button__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nconst PricingPage = ({ onBack })=>{\n    const { user } = (0,_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { subscription } = (0,_components_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__.useSubscription)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-12\",\n            children: [\n                onBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                        onClick: onBack,\n                        variant: \"ghost\",\n                        className: \"text-gray-300 hover:text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_9__.ArrowLeft, {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 15\n                            }, undefined),\n                            \"Back to Home\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl sm:text-5xl md:text-6xl font-bold text-white mb-6\",\n                            children: \"Choose Your Plan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                            children: \"Unlock the full power of YouTube subtitle extraction with our credit-based plans. Each video extraction costs 2 credits. Start with any plan and upgrade anytime.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        !user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_LoginButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: \"lg\",\n                                className: \"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700\",\n                                children: \"Sign in to Get Started\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\",\n                    children: Object.keys(_lib_stripe__WEBPACK_IMPORTED_MODULE_4__.PRICING_TIERS).map((tier, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: index * 0.1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PricingCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                tier: tier,\n                                isCurrentPlan: subscription?.tier === tier && subscription?.status === 'active'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, undefined)\n                        }, tier, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    className: \"bg-slate-800/80 backdrop-blur-sm rounded-xl p-8 border border-slate-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6 text-center\",\n                            children: \"Why Choose Our Premium Plans?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-2\",\n                                            children: \"Lightning Fast\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Extract subtitles in seconds with our optimized processing pipeline\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: \"\\uD83C\\uDFAF\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-2\",\n                                            children: \"High Accuracy\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Advanced AI processing ensures the highest quality subtitle extraction\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: \"\\uD83D\\uDD12\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-2\",\n                                            children: \"Secure & Private\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Your data is processed securely and never stored on our servers\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.6\n                    },\n                    className: \"mt-12 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Frequently Asked Questions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/80 backdrop-blur-sm rounded-lg p-6 border border-slate-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-2\",\n                                            children: \"Can I change plans anytime?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/80 backdrop-blur-sm rounded-lg p-6 border border-slate-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-2\",\n                                            children: \"What payment methods do you accept?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"We accept all major credit cards, debit cards, and digital wallets through Stripe.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/80 backdrop-blur-sm rounded-lg p-6 border border-slate-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-2\",\n                                            children: \"How do credits work?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Each video extraction costs 2 credits (representing 2 API calls to our proxy server). Credits reset monthly with your subscription.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/80 backdrop-blur-sm rounded-lg p-6 border border-slate-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-2\",\n                                            children: \"What happens if I run out of credits?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"You can upgrade your plan anytime for more credits, or wait until your next billing cycle when credits reset.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/pricing/PricingPage.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PricingPage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/pricing/PricingPage.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/avatar.tsx":
/*!**********************************!*\
  !*** ./components/ui/avatar.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"@radix-ui/react-avatar\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/avatar.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined));\nAvatar.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Root.displayName;\nconst AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Image, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"aspect-square h-full w-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/avatar.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nAvatarImage.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Image.displayName;\nconst AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Fallback, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex h-full w-full items-center justify-center rounded-full bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/avatar.tsx\",\n        lineNumber: 37,\n        columnNumber: 3\n    }, undefined));\nAvatarFallback.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Fallback.displayName;\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/avatar.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-slot */ \"@radix-ui/react-slot\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-xl border bg-card text-card-foreground shadow\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/card.tsx\",\n        lineNumber: 48,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/card.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/card.tsx\",\n        lineNumber: 68,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/dropdown-menu.tsx":
/*!*****************************************!*\
  !*** ./components/ui/dropdown-menu.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"@radix-ui/react-dropdown-menu\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-icons */ \"@radix-ui/react-icons\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_3__.ChevronRightIcon, {\n                className: \"ml-auto h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n                lineNumber: 39,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 29,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 49,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\", \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n            lineNumber: 66,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 65,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 86,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_3__.CheckIcon, {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n                lineNumber: 111,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 102,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_3__.DotFilledIcon, {\n                        className: \"h-4 w-4 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n                lineNumber: 134,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 126,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 150,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 166,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/components/ui/dropdown-menu.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvdWkvZHJvcGRvd24tbWVudS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ3dDO0FBS3hDO0FBRUU7QUFFaEMsTUFBTU0sZUFBZUwsK0RBQTBCO0FBRS9DLE1BQU1PLHNCQUFzQlAsa0VBQTZCO0FBRXpELE1BQU1TLG9CQUFvQlQsZ0VBQTJCO0FBRXJELE1BQU1XLHFCQUFxQlgsaUVBQTRCO0FBRXZELE1BQU1hLGtCQUFrQmIsOERBQXlCO0FBRWpELE1BQU1lLHlCQUF5QmYscUVBQWdDO0FBRS9ELE1BQU1pQix1Q0FBeUJsQiw2Q0FBZ0IsQ0FLN0MsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFQyxLQUFLLEVBQUVDLFFBQVEsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMzQyw4REFBQ3ZCLHFFQUFnQztRQUMvQnVCLEtBQUtBO1FBQ0xKLFdBQVdmLDhDQUFFQSxDQUNYLHdJQUNBZ0IsU0FBUyxRQUNURDtRQUVELEdBQUdHLEtBQUs7O1lBRVJEOzBCQUNELDhEQUFDbkIsbUVBQWdCQTtnQkFBQ2lCLFdBQVU7Ozs7Ozs7Ozs7OztBQUdoQ0YsdUJBQXVCUSxXQUFXLEdBQ2hDekIscUVBQWdDLENBQUN5QixXQUFXO0FBRTlDLE1BQU1DLHVDQUF5QjNCLDZDQUFnQixDQUc3QyxDQUFDLEVBQUVvQixTQUFTLEVBQUUsR0FBR0csT0FBTyxFQUFFQyxvQkFDMUIsOERBQUN2QixxRUFBZ0M7UUFDL0J1QixLQUFLQTtRQUNMSixXQUFXZiw4Q0FBRUEsQ0FDWCx5YkFDQWU7UUFFRCxHQUFHRyxLQUFLOzs7Ozs7QUFHYkksdUJBQXVCRCxXQUFXLEdBQ2hDekIscUVBQWdDLENBQUN5QixXQUFXO0FBRTlDLE1BQU1HLG9DQUFzQjdCLDZDQUFnQixDQUcxQyxDQUFDLEVBQUVvQixTQUFTLEVBQUVVLGFBQWEsQ0FBQyxFQUFFLEdBQUdQLE9BQU8sRUFBRUMsb0JBQzFDLDhEQUFDdkIsaUVBQTRCO2tCQUMzQiw0RUFBQ0Esa0VBQTZCO1lBQzVCdUIsS0FBS0E7WUFDTE0sWUFBWUE7WUFDWlYsV0FBV2YsOENBQUVBLENBQ1gsd0dBQ0Esb1ZBQ0FlO1lBRUQsR0FBR0csS0FBSzs7Ozs7Ozs7Ozs7QUFJZk0sb0JBQW9CSCxXQUFXLEdBQUd6QixrRUFBNkIsQ0FBQ3lCLFdBQVc7QUFFM0UsTUFBTU0saUNBQW1CaEMsNkNBQWdCLENBS3ZDLENBQUMsRUFBRW9CLFNBQVMsRUFBRUMsS0FBSyxFQUFFLEdBQUdFLE9BQU8sRUFBRUMsb0JBQ2pDLDhEQUFDdkIsK0RBQTBCO1FBQ3pCdUIsS0FBS0E7UUFDTEosV0FBV2YsOENBQUVBLENBQ1gsbU9BQ0FnQixTQUFTLFFBQ1REO1FBRUQsR0FBR0csS0FBSzs7Ozs7O0FBR2JTLGlCQUFpQk4sV0FBVyxHQUFHekIsK0RBQTBCLENBQUN5QixXQUFXO0FBRXJFLE1BQU1RLHlDQUEyQmxDLDZDQUFnQixDQUcvQyxDQUFDLEVBQUVvQixTQUFTLEVBQUVFLFFBQVEsRUFBRWEsT0FBTyxFQUFFLEdBQUdaLE9BQU8sRUFBRUMsb0JBQzdDLDhEQUFDdkIsdUVBQWtDO1FBQ2pDdUIsS0FBS0E7UUFDTEosV0FBV2YsOENBQUVBLENBQ1gsd09BQ0FlO1FBRUZlLFNBQVNBO1FBQ1IsR0FBR1osS0FBSzs7MEJBRVQsOERBQUNjO2dCQUFLakIsV0FBVTswQkFDZCw0RUFBQ25CLHdFQUFtQzs4QkFDbEMsNEVBQUNDLDREQUFTQTt3QkFBQ2tCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFHeEJFOzs7Ozs7O0FBR0xZLHlCQUF5QlIsV0FBVyxHQUNsQ3pCLHVFQUFrQyxDQUFDeUIsV0FBVztBQUVoRCxNQUFNYSxzQ0FBd0J2Qyw2Q0FBZ0IsQ0FHNUMsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFRSxRQUFRLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDcEMsOERBQUN2QixvRUFBK0I7UUFDOUJ1QixLQUFLQTtRQUNMSixXQUFXZiw4Q0FBRUEsQ0FDWCx3T0FDQWU7UUFFRCxHQUFHRyxLQUFLOzswQkFFVCw4REFBQ2M7Z0JBQUtqQixXQUFVOzBCQUNkLDRFQUFDbkIsd0VBQW1DOzhCQUNsQyw0RUFBQ0csZ0VBQWFBO3dCQUFDZ0IsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztZQUc1QkU7Ozs7Ozs7QUFHTGlCLHNCQUFzQmIsV0FBVyxHQUFHekIsb0VBQStCLENBQUN5QixXQUFXO0FBRS9FLE1BQU1lLGtDQUFvQnpDLDZDQUFnQixDQUt4QyxDQUFDLEVBQUVvQixTQUFTLEVBQUVDLEtBQUssRUFBRSxHQUFHRSxPQUFPLEVBQUVDLG9CQUNqQyw4REFBQ3ZCLGdFQUEyQjtRQUMxQnVCLEtBQUtBO1FBQ0xKLFdBQVdmLDhDQUFFQSxDQUNYLHFDQUNBZ0IsU0FBUyxRQUNURDtRQUVELEdBQUdHLEtBQUs7Ozs7OztBQUdia0Isa0JBQWtCZixXQUFXLEdBQUd6QixnRUFBMkIsQ0FBQ3lCLFdBQVc7QUFFdkUsTUFBTWlCLHNDQUF3QjNDLDZDQUFnQixDQUc1QyxDQUFDLEVBQUVvQixTQUFTLEVBQUUsR0FBR0csT0FBTyxFQUFFQyxvQkFDMUIsOERBQUN2QixvRUFBK0I7UUFDOUJ1QixLQUFLQTtRQUNMSixXQUFXZiw4Q0FBRUEsQ0FBQyw0QkFBNEJlO1FBQ3pDLEdBQUdHLEtBQUs7Ozs7OztBQUdib0Isc0JBQXNCakIsV0FBVyxHQUFHekIsb0VBQStCLENBQUN5QixXQUFXO0FBRS9FLE1BQU1tQix1QkFBdUIsQ0FBQyxFQUM1QnpCLFNBQVMsRUFDVCxHQUFHRyxPQUNtQztJQUN0QyxxQkFDRSw4REFBQ2M7UUFDQ2pCLFdBQVdmLDhDQUFFQSxDQUFDLDhDQUE4Q2U7UUFDM0QsR0FBR0csS0FBSzs7Ozs7O0FBR2Y7QUFDQXNCLHFCQUFxQm5CLFdBQVcsR0FBRztBQWtCbEMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kaW5lc2hzL0RvY3VtZW50cy9EZXYvUHJvamVjdHMvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ZVFN1YnRpdGxlRXh0cmFjdG9yL2NvbXBvbmVudHMvdWkvZHJvcGRvd24tbWVudS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCAqIGFzIERyb3Bkb3duTWVudVByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWRyb3Bkb3duLW1lbnVcIlxuaW1wb3J0IHtcbiAgQ2hlY2tJY29uLFxuICBDaGV2cm9uUmlnaHRJY29uLFxuICBEb3RGaWxsZWRJY29uLFxufSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWljb25zXCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBEcm9wZG93bk1lbnUgPSBEcm9wZG93bk1lbnVQcmltaXRpdmUuUm9vdFxuXG5jb25zdCBEcm9wZG93bk1lbnVUcmlnZ2VyID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLlRyaWdnZXJcblxuY29uc3QgRHJvcGRvd25NZW51R3JvdXAgPSBEcm9wZG93bk1lbnVQcmltaXRpdmUuR3JvdXBcblxuY29uc3QgRHJvcGRvd25NZW51UG9ydGFsID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLlBvcnRhbFxuXG5jb25zdCBEcm9wZG93bk1lbnVTdWIgPSBEcm9wZG93bk1lbnVQcmltaXRpdmUuU3ViXG5cbmNvbnN0IERyb3Bkb3duTWVudVJhZGlvR3JvdXAgPSBEcm9wZG93bk1lbnVQcmltaXRpdmUuUmFkaW9Hcm91cFxuXG5jb25zdCBEcm9wZG93bk1lbnVTdWJUcmlnZ2VyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLlN1YlRyaWdnZXI+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5TdWJUcmlnZ2VyPiAmIHtcbiAgICBpbnNldD86IGJvb2xlYW5cbiAgfVxuPigoeyBjbGFzc05hbWUsIGluc2V0LCBjaGlsZHJlbiwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxEcm9wZG93bk1lbnVQcmltaXRpdmUuU3ViVHJpZ2dlclxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcImZsZXggY3Vyc29yLWRlZmF1bHQgc2VsZWN0LW5vbmUgaXRlbXMtY2VudGVyIHJvdW5kZWQtc20gcHgtMiBweS0xLjUgdGV4dC1zbSBvdXRsaW5lLW5vbmUgZm9jdXM6YmctYWNjZW50IGRhdGEtW3N0YXRlPW9wZW5dOmJnLWFjY2VudFwiLFxuICAgICAgaW5zZXQgJiYgXCJwbC04XCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgPlxuICAgIHtjaGlsZHJlbn1cbiAgICA8Q2hldnJvblJpZ2h0SWNvbiBjbGFzc05hbWU9XCJtbC1hdXRvIGgtNCB3LTRcIiAvPlxuICA8L0Ryb3Bkb3duTWVudVByaW1pdGl2ZS5TdWJUcmlnZ2VyPlxuKSlcbkRyb3Bkb3duTWVudVN1YlRyaWdnZXIuZGlzcGxheU5hbWUgPVxuICBEcm9wZG93bk1lbnVQcmltaXRpdmUuU3ViVHJpZ2dlci5kaXNwbGF5TmFtZVxuXG5jb25zdCBEcm9wZG93bk1lbnVTdWJDb250ZW50ID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLlN1YkNvbnRlbnQ+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5TdWJDb250ZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLlN1YkNvbnRlbnRcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJ6LTUwIG1pbi13LVs4cmVtXSBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC1tZCBib3JkZXIgYmctcG9wb3ZlciBwLTEgdGV4dC1wb3BvdmVyLWZvcmVncm91bmQgc2hhZG93LWxnIGRhdGEtW3N0YXRlPW9wZW5dOmFuaW1hdGUtaW4gZGF0YS1bc3RhdGU9Y2xvc2VkXTphbmltYXRlLW91dCBkYXRhLVtzdGF0ZT1jbG9zZWRdOmZhZGUtb3V0LTAgZGF0YS1bc3RhdGU9b3Blbl06ZmFkZS1pbi0wIGRhdGEtW3N0YXRlPWNsb3NlZF06em9vbS1vdXQtOTUgZGF0YS1bc3RhdGU9b3Blbl06em9vbS1pbi05NSBkYXRhLVtzaWRlPWJvdHRvbV06c2xpZGUtaW4tZnJvbS10b3AtMiBkYXRhLVtzaWRlPWxlZnRdOnNsaWRlLWluLWZyb20tcmlnaHQtMiBkYXRhLVtzaWRlPXJpZ2h0XTpzbGlkZS1pbi1mcm9tLWxlZnQtMiBkYXRhLVtzaWRlPXRvcF06c2xpZGUtaW4tZnJvbS1ib3R0b20tMlwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuRHJvcGRvd25NZW51U3ViQ29udGVudC5kaXNwbGF5TmFtZSA9XG4gIERyb3Bkb3duTWVudVByaW1pdGl2ZS5TdWJDb250ZW50LmRpc3BsYXlOYW1lXG5cbmNvbnN0IERyb3Bkb3duTWVudUNvbnRlbnQgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBEcm9wZG93bk1lbnVQcmltaXRpdmUuQ29udGVudD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLkNvbnRlbnQ+XG4+KCh7IGNsYXNzTmFtZSwgc2lkZU9mZnNldCA9IDQsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLlBvcnRhbD5cbiAgICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLkNvbnRlbnRcbiAgICAgIHJlZj17cmVmfVxuICAgICAgc2lkZU9mZnNldD17c2lkZU9mZnNldH1cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiei01MCBtaW4tdy1bOHJlbV0gb3ZlcmZsb3ctaGlkZGVuIHJvdW5kZWQtbWQgYm9yZGVyIGJnLXBvcG92ZXIgcC0xIHRleHQtcG9wb3Zlci1mb3JlZ3JvdW5kIHNoYWRvdy1tZFwiLFxuICAgICAgICBcImRhdGEtW3N0YXRlPW9wZW5dOmFuaW1hdGUtaW4gZGF0YS1bc3RhdGU9Y2xvc2VkXTphbmltYXRlLW91dCBkYXRhLVtzdGF0ZT1jbG9zZWRdOmZhZGUtb3V0LTAgZGF0YS1bc3RhdGU9b3Blbl06ZmFkZS1pbi0wIGRhdGEtW3N0YXRlPWNsb3NlZF06em9vbS1vdXQtOTUgZGF0YS1bc3RhdGU9b3Blbl06em9vbS1pbi05NSBkYXRhLVtzaWRlPWJvdHRvbV06c2xpZGUtaW4tZnJvbS10b3AtMiBkYXRhLVtzaWRlPWxlZnRdOnNsaWRlLWluLWZyb20tcmlnaHQtMiBkYXRhLVtzaWRlPXJpZ2h0XTpzbGlkZS1pbi1mcm9tLWxlZnQtMiBkYXRhLVtzaWRlPXRvcF06c2xpZGUtaW4tZnJvbS1ib3R0b20tMlwiLFxuICAgICAgICBjbGFzc05hbWVcbiAgICAgICl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgPC9Ecm9wZG93bk1lbnVQcmltaXRpdmUuUG9ydGFsPlxuKSlcbkRyb3Bkb3duTWVudUNvbnRlbnQuZGlzcGxheU5hbWUgPSBEcm9wZG93bk1lbnVQcmltaXRpdmUuQ29udGVudC5kaXNwbGF5TmFtZVxuXG5jb25zdCBEcm9wZG93bk1lbnVJdGVtID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLkl0ZW0+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5JdGVtPiAmIHtcbiAgICBpbnNldD86IGJvb2xlYW5cbiAgfVxuPigoeyBjbGFzc05hbWUsIGluc2V0LCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPERyb3Bkb3duTWVudVByaW1pdGl2ZS5JdGVtXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwicmVsYXRpdmUgZmxleCBjdXJzb3ItZGVmYXVsdCBzZWxlY3Qtbm9uZSBpdGVtcy1jZW50ZXIgcm91bmRlZC1zbSBweC0yIHB5LTEuNSB0ZXh0LXNtIG91dGxpbmUtbm9uZSB0cmFuc2l0aW9uLWNvbG9ycyBmb2N1czpiZy1hY2NlbnQgZm9jdXM6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBkYXRhLVtkaXNhYmxlZF06cG9pbnRlci1ldmVudHMtbm9uZSBkYXRhLVtkaXNhYmxlZF06b3BhY2l0eS01MFwiLFxuICAgICAgaW5zZXQgJiYgXCJwbC04XCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5Ecm9wZG93bk1lbnVJdGVtLmRpc3BsYXlOYW1lID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLkl0ZW0uZGlzcGxheU5hbWVcblxuY29uc3QgRHJvcGRvd25NZW51Q2hlY2tib3hJdGVtID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLkNoZWNrYm94SXRlbT4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLkNoZWNrYm94SXRlbT5cbj4oKHsgY2xhc3NOYW1lLCBjaGlsZHJlbiwgY2hlY2tlZCwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxEcm9wZG93bk1lbnVQcmltaXRpdmUuQ2hlY2tib3hJdGVtXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwicmVsYXRpdmUgZmxleCBjdXJzb3ItZGVmYXVsdCBzZWxlY3Qtbm9uZSBpdGVtcy1jZW50ZXIgcm91bmRlZC1zbSBweS0xLjUgcGwtOCBwci0yIHRleHQtc20gb3V0bGluZS1ub25lIHRyYW5zaXRpb24tY29sb3JzIGZvY3VzOmJnLWFjY2VudCBmb2N1czp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGRhdGEtW2Rpc2FibGVkXTpwb2ludGVyLWV2ZW50cy1ub25lIGRhdGEtW2Rpc2FibGVkXTpvcGFjaXR5LTUwXCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIGNoZWNrZWQ9e2NoZWNrZWR9XG4gICAgey4uLnByb3BzfVxuICA+XG4gICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0yIGZsZXggaC0zLjUgdy0zLjUgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLkl0ZW1JbmRpY2F0b3I+XG4gICAgICAgIDxDaGVja0ljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICA8L0Ryb3Bkb3duTWVudVByaW1pdGl2ZS5JdGVtSW5kaWNhdG9yPlxuICAgIDwvc3Bhbj5cbiAgICB7Y2hpbGRyZW59XG4gIDwvRHJvcGRvd25NZW51UHJpbWl0aXZlLkNoZWNrYm94SXRlbT5cbikpXG5Ecm9wZG93bk1lbnVDaGVja2JveEl0ZW0uZGlzcGxheU5hbWUgPVxuICBEcm9wZG93bk1lbnVQcmltaXRpdmUuQ2hlY2tib3hJdGVtLmRpc3BsYXlOYW1lXG5cbmNvbnN0IERyb3Bkb3duTWVudVJhZGlvSXRlbSA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5SYWRpb0l0ZW0+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5SYWRpb0l0ZW0+XG4+KCh7IGNsYXNzTmFtZSwgY2hpbGRyZW4sIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLlJhZGlvSXRlbVxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInJlbGF0aXZlIGZsZXggY3Vyc29yLWRlZmF1bHQgc2VsZWN0LW5vbmUgaXRlbXMtY2VudGVyIHJvdW5kZWQtc20gcHktMS41IHBsLTggcHItMiB0ZXh0LXNtIG91dGxpbmUtbm9uZSB0cmFuc2l0aW9uLWNvbG9ycyBmb2N1czpiZy1hY2NlbnQgZm9jdXM6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBkYXRhLVtkaXNhYmxlZF06cG9pbnRlci1ldmVudHMtbm9uZSBkYXRhLVtkaXNhYmxlZF06b3BhY2l0eS01MFwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gID5cbiAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTIgZmxleCBoLTMuNSB3LTMuNSBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgIDxEcm9wZG93bk1lbnVQcmltaXRpdmUuSXRlbUluZGljYXRvcj5cbiAgICAgICAgPERvdEZpbGxlZEljb24gY2xhc3NOYW1lPVwiaC00IHctNCBmaWxsLWN1cnJlbnRcIiAvPlxuICAgICAgPC9Ecm9wZG93bk1lbnVQcmltaXRpdmUuSXRlbUluZGljYXRvcj5cbiAgICA8L3NwYW4+XG4gICAge2NoaWxkcmVufVxuICA8L0Ryb3Bkb3duTWVudVByaW1pdGl2ZS5SYWRpb0l0ZW0+XG4pKVxuRHJvcGRvd25NZW51UmFkaW9JdGVtLmRpc3BsYXlOYW1lID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLlJhZGlvSXRlbS5kaXNwbGF5TmFtZVxuXG5jb25zdCBEcm9wZG93bk1lbnVMYWJlbCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5MYWJlbD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLkxhYmVsPiAmIHtcbiAgICBpbnNldD86IGJvb2xlYW5cbiAgfVxuPigoeyBjbGFzc05hbWUsIGluc2V0LCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPERyb3Bkb3duTWVudVByaW1pdGl2ZS5MYWJlbFxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInB4LTIgcHktMS41IHRleHQtc20gZm9udC1zZW1pYm9sZFwiLFxuICAgICAgaW5zZXQgJiYgXCJwbC04XCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5Ecm9wZG93bk1lbnVMYWJlbC5kaXNwbGF5TmFtZSA9IERyb3Bkb3duTWVudVByaW1pdGl2ZS5MYWJlbC5kaXNwbGF5TmFtZVxuXG5jb25zdCBEcm9wZG93bk1lbnVTZXBhcmF0b3IgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBEcm9wZG93bk1lbnVQcmltaXRpdmUuU2VwYXJhdG9yPixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBEcm9wZG93bk1lbnVQcmltaXRpdmUuU2VwYXJhdG9yPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLlNlcGFyYXRvclxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXCItbXgtMSBteS0xIGgtcHggYmctbXV0ZWRcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuRHJvcGRvd25NZW51U2VwYXJhdG9yLmRpc3BsYXlOYW1lID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLlNlcGFyYXRvci5kaXNwbGF5TmFtZVxuXG5jb25zdCBEcm9wZG93bk1lbnVTaG9ydGN1dCA9ICh7XG4gIGNsYXNzTmFtZSxcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxTcGFuRWxlbWVudD4pID0+IHtcbiAgcmV0dXJuIChcbiAgICA8c3BhblxuICAgICAgY2xhc3NOYW1lPXtjbihcIm1sLWF1dG8gdGV4dC14cyB0cmFja2luZy13aWRlc3Qgb3BhY2l0eS02MFwiLCBjbGFzc05hbWUpfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn1cbkRyb3Bkb3duTWVudVNob3J0Y3V0LmRpc3BsYXlOYW1lID0gXCJEcm9wZG93bk1lbnVTaG9ydGN1dFwiXG5cbmV4cG9ydCB7XG4gIERyb3Bkb3duTWVudSxcbiAgRHJvcGRvd25NZW51VHJpZ2dlcixcbiAgRHJvcGRvd25NZW51Q29udGVudCxcbiAgRHJvcGRvd25NZW51SXRlbSxcbiAgRHJvcGRvd25NZW51Q2hlY2tib3hJdGVtLFxuICBEcm9wZG93bk1lbnVSYWRpb0l0ZW0sXG4gIERyb3Bkb3duTWVudUxhYmVsLFxuICBEcm9wZG93bk1lbnVTZXBhcmF0b3IsXG4gIERyb3Bkb3duTWVudVNob3J0Y3V0LFxuICBEcm9wZG93bk1lbnVHcm91cCxcbiAgRHJvcGRvd25NZW51UG9ydGFsLFxuICBEcm9wZG93bk1lbnVTdWIsXG4gIERyb3Bkb3duTWVudVN1YkNvbnRlbnQsXG4gIERyb3Bkb3duTWVudVN1YlRyaWdnZXIsXG4gIERyb3Bkb3duTWVudVJhZGlvR3JvdXAsXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJEcm9wZG93bk1lbnVQcmltaXRpdmUiLCJDaGVja0ljb24iLCJDaGV2cm9uUmlnaHRJY29uIiwiRG90RmlsbGVkSWNvbiIsImNuIiwiRHJvcGRvd25NZW51IiwiUm9vdCIsIkRyb3Bkb3duTWVudVRyaWdnZXIiLCJUcmlnZ2VyIiwiRHJvcGRvd25NZW51R3JvdXAiLCJHcm91cCIsIkRyb3Bkb3duTWVudVBvcnRhbCIsIlBvcnRhbCIsIkRyb3Bkb3duTWVudVN1YiIsIlN1YiIsIkRyb3Bkb3duTWVudVJhZGlvR3JvdXAiLCJSYWRpb0dyb3VwIiwiRHJvcGRvd25NZW51U3ViVHJpZ2dlciIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJpbnNldCIsImNoaWxkcmVuIiwicHJvcHMiLCJyZWYiLCJTdWJUcmlnZ2VyIiwiZGlzcGxheU5hbWUiLCJEcm9wZG93bk1lbnVTdWJDb250ZW50IiwiU3ViQ29udGVudCIsIkRyb3Bkb3duTWVudUNvbnRlbnQiLCJzaWRlT2Zmc2V0IiwiQ29udGVudCIsIkRyb3Bkb3duTWVudUl0ZW0iLCJJdGVtIiwiRHJvcGRvd25NZW51Q2hlY2tib3hJdGVtIiwiY2hlY2tlZCIsIkNoZWNrYm94SXRlbSIsInNwYW4iLCJJdGVtSW5kaWNhdG9yIiwiRHJvcGRvd25NZW51UmFkaW9JdGVtIiwiUmFkaW9JdGVtIiwiRHJvcGRvd25NZW51TGFiZWwiLCJMYWJlbCIsIkRyb3Bkb3duTWVudVNlcGFyYXRvciIsIlNlcGFyYXRvciIsIkRyb3Bkb3duTWVudVNob3J0Y3V0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/stripe.ts":
/*!***********************!*\
  !*** ./lib/stripe.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PRICING_TIERS: () => (/* binding */ PRICING_TIERS),\n/* harmony export */   stripePromise: () => (/* binding */ stripePromise)\n/* harmony export */ });\n/* harmony import */ var _stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @stripe/stripe-js */ \"@stripe/stripe-js\");\n/* harmony import */ var _stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst stripePublishableKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzYXhpaWFscmhwcWRsY3BtZXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDA5NDU3MCwiZXhwIjoyMDY1NjcwNTcwfQ.pPJz2h3Y-tXgzFuKX2hMYPEnqoaHd_p1G5dNVbQSccg\";\nif (!stripePublishableKey && \"undefined\" !== 'undefined') {}\nconst stripePromise = stripePublishableKey ? (0,_stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__.loadStripe)(stripePublishableKey) : null;\n// Pricing configuration - High-Bandwidth Proxy Credit System\n// Credit system: 1 credit = 1 API call (~20MB bandwidth, $0.10 proxy cost)\n// - Getting available languages = 1 credit\n// - Downloading subtitles = 1 credit\n// - Total per complete video extraction = 2 credits\n// Proxy server costs $5/GB (~$0.10 per API call), priced at $0.15+ per credit for profitability\nconst PRICING_TIERS = {\n    starter: {\n        name: 'Starter',\n        price: 10,\n        priceId: process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID || 'price_starter_monthly',\n        features: [\n            '50 credits/month (~25 complete extractions)',\n            'VTT and TXT format downloads',\n            'Auto-generated caption support',\n            'Basic language detection',\n            'Email support',\n            'Credits valid for 6 months'\n        ],\n        limits: {\n            creditsPerMonth: 50,\n            creditsPerAction: 1,\n            actionsPerExtraction: 2,\n            bandwidthPerCredit: 20,\n            costPerCredit: 0.20 // $0.20 per credit\n        },\n        popular: false\n    },\n    pro: {\n        name: 'Pro',\n        price: 30,\n        priceId: process.env.NEXT_PUBLIC_STRIPE_PRO_PRICE_ID || 'price_pro_monthly',\n        features: [\n            '200 credits/month (~100 complete extractions)',\n            'All format downloads (VTT, TXT)',\n            'Manual and auto-generated captions',\n            'Advanced language detection',\n            'Batch processing',\n            'Priority support',\n            'Credits valid for 6 months'\n        ],\n        limits: {\n            creditsPerMonth: 200,\n            creditsPerAction: 1,\n            actionsPerExtraction: 2,\n            bandwidthPerCredit: 20,\n            costPerCredit: 0.15 // $0.15 per credit\n        },\n        popular: true\n    },\n    creator: {\n        name: 'Creator',\n        price: 75,\n        priceId: process.env.NEXT_PUBLIC_STRIPE_CREATOR_PRICE_ID || 'price_creator_monthly',\n        features: [\n            '600 credits/month (~300 complete extractions)',\n            'All format downloads',\n            'Manual and auto-generated captions',\n            'Advanced language detection',\n            'Batch processing',\n            'API access (coming soon)',\n            'Custom integrations',\n            'Priority support',\n            'Credits valid for 6 months'\n        ],\n        limits: {\n            creditsPerMonth: 600,\n            creditsPerAction: 1,\n            actionsPerExtraction: 2,\n            bandwidthPerCredit: 20,\n            costPerCredit: 0.125 // $0.125 per credit\n        },\n        popular: false\n    },\n    enterprise: {\n        name: 'Enterprise',\n        price: 150,\n        priceId: process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID || 'price_enterprise_monthly',\n        features: [\n            '1200 credits/month (~600 complete extractions)',\n            'All format downloads',\n            'Manual and auto-generated captions',\n            'Advanced language detection',\n            'Batch processing',\n            'Full API access',\n            'Custom integrations',\n            'Dedicated support',\n            'Credits valid for 6 months',\n            'Volume discounts available'\n        ],\n        limits: {\n            creditsPerMonth: 1200,\n            creditsPerAction: 1,\n            actionsPerExtraction: 2,\n            bandwidthPerCredit: 20,\n            costPerCredit: 0.125 // $0.125 per credit\n        },\n        popular: false\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/stripe.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabaseClient: () => (/* binding */ createServerSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://vsaxiialrhpqdlcpmetn.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzYXhpaWFscmhwcWRsY3BtZXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwOTQ1NzAsImV4cCI6MjA2NTY3MDU3MH0.MDe8-SLF1-73hqcunFsgOEvDYek1ku7Ap_A1UNardA8\" || 0;\n// Only throw error in production runtime (not during build)\nif ((!supabaseUrl || !supabaseAnonKey || supabaseUrl.includes('placeholder')) && \"development\" === 'production' && 0) {}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true,\n        flowType: 'pkce'\n    }\n});\n// Server-side client for API routes (Node.js environment)\nconst createServerSupabaseClient = ()=>{\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!serviceRoleKey) {\n        throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://vsaxiialrhpqdlcpmetn.supabase.co\" || 0, serviceRoleKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/supabase.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2xpYi91dGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEM7QUFDSjtBQUVqQyxTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kaW5lc2hzL0RvY3VtZW50cy9EZXYvUHJvamVjdHMvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ZVFN1YnRpdGxlRXh0cmFjdG9yL2xpYi91dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/utils.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpricing&preferredRegion=&absolutePagePath=.%2Fpages%2Fpricing.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpricing&preferredRegion=&absolutePagePath=.%2Fpages%2Fpricing.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/pricing.tsx */ \"(pages-dir-node)/./pages/pricing.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/pricing\",\n        pathname: \"/pricing\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_pricing_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpricing&preferredRegion=&absolutePagePath=.%2Fpages%2Fpricing.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/contexts/AuthContext */ \"(pages-dir-node)/./components/contexts/AuthContext.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_1__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n([react_hot_toast__WEBPACK_IMPORTED_MODULE_1__, _components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/_app.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: '#1e293b',\n                        color: '#f1f5f9',\n                        border: '1px solid #475569'\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/_app.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQzBDO0FBQ3dCO0FBQ25DO0FBRWhCLFNBQVNFLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNILDBFQUFZQTs7MEJBQ1gsOERBQUNFO2dCQUFXLEdBQUdDLFNBQVM7Ozs7OzswQkFDeEIsOERBQUNKLG9EQUFPQTtnQkFDTkssVUFBUztnQkFDVEMsY0FBYztvQkFDWkMsVUFBVTtvQkFDVkMsT0FBTzt3QkFDTEMsWUFBWTt3QkFDWkMsT0FBTzt3QkFDUEMsUUFBUTtvQkFDVjtnQkFDRjs7Ozs7Ozs7Ozs7O0FBSVIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kaW5lc2hzL0RvY3VtZW50cy9EZXYvUHJvamVjdHMvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ZVFN1YnRpdGxlRXh0cmFjdG9yL3BhZ2VzL19hcHAudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAncmVhY3QtaG90LXRvYXN0JztcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJy4uL2NvbXBvbmVudHMvY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgICA8VG9hc3RlclxuICAgICAgICBwb3NpdGlvbj1cInRvcC1yaWdodFwiXG4gICAgICAgIHRvYXN0T3B0aW9ucz17e1xuICAgICAgICAgIGR1cmF0aW9uOiA0MDAwLFxuICAgICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzFlMjkzYicsXG4gICAgICAgICAgICBjb2xvcjogJyNmMWY1ZjknLFxuICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkICM0NzU1NjknLFxuICAgICAgICAgIH0sXG4gICAgICAgIH19XG4gICAgICAvPlxuICAgIDwvQXV0aFByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRvYXN0ZXIiLCJBdXRoUHJvdmlkZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJwb3NpdGlvbiIsInRvYXN0T3B0aW9ucyIsImR1cmF0aW9uIiwic3R5bGUiLCJiYWNrZ3JvdW5kIiwiY29sb3IiLCJib3JkZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/pricing.tsx":
/*!***************************!*\
  !*** ./pages/pricing.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Pricing)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Header */ \"(pages-dir-node)/./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Footer */ \"(pages-dir-node)/./components/Footer.tsx\");\n/* harmony import */ var _components_pricing_PricingPage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/pricing/PricingPage */ \"(pages-dir-node)/./components/pricing/PricingPage.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_Header__WEBPACK_IMPORTED_MODULE_3__, _components_Footer__WEBPACK_IMPORTED_MODULE_4__, _components_pricing_PricingPage__WEBPACK_IMPORTED_MODULE_5__]);\n([_components_Header__WEBPACK_IMPORTED_MODULE_3__, _components_Footer__WEBPACK_IMPORTED_MODULE_4__, _components_pricing_PricingPage__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction Pricing() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleBackToHome = ()=>{\n        router.push('/');\n    };\n    const handleNavigate = (view)=>{\n        router.push(`/${view}`);\n    };\n    const handleFeedback = ()=>{\n        if (false) {}\n    };\n    const handleTermsClick = ()=>{\n        router.push('/terms');\n    };\n    const handlePrivacyClick = ()=>{\n        router.push('/privacy');\n    };\n    const handleDisclaimerClick = ()=>{\n        router.push('/disclaimer');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Pricing - DownloadYTSubtitles\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/pricing.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Choose your plan for DownloadYTSubtitles - YouTube subtitle extractor service.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/pricing.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/pricing.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/pricing.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/pricing.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen font-sans\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        currentView: \"pricing\",\n                        onNavigate: handleNavigate,\n                        onFeedback: handleFeedback\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/pricing.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pricing_PricingPage__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onBack: handleBackToHome\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/pricing.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        onTermsClick: handleTermsClick,\n                        onPrivacyClick: handlePrivacyClick,\n                        onDisclaimerClick: handleDisclaimerClick\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/pricing.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Dev/Projects/YTSubtitleExtractor/YTSubtitleExtractor/pages/pricing.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/pricing.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=ArrowLeft!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!**************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowLeft!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ArrowLeft: () => (/* reexport safe */ _icons_arrow_left_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _icons_arrow_left_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/arrow-left.js */ "(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js");



/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=Check,Crown,Star,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Check,Crown,Star,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Check: () => (/* reexport safe */ _icons_check_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Crown: () => (/* reexport safe */ _icons_crown_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Star: () => (/* reexport safe */ _icons_star_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Zap: () => (/* reexport safe */ _icons_zap_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_check_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _icons_crown_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/crown.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _icons_star_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/star.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _icons_zap_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/zap.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUNoZWNrLENyb3duLFN0YXIsWmFwIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDbUQ7QUFDQTtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvZGluZXNocy9Eb2N1bWVudHMvRGV2L1Byb2plY3RzL1lUU3VidGl0bGVFeHRyYWN0b3IvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hlY2sgfSBmcm9tIFwiLi9pY29ucy9jaGVjay5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENyb3duIH0gZnJvbSBcIi4vaWNvbnMvY3Jvd24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTdGFyIH0gZnJvbSBcIi4vaWNvbnMvc3Rhci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFphcCB9IGZyb20gXCIuL2ljb25zL3phcC5qc1wiIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=Check,Crown,Star,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=CreditCard,Crown,LogOut,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=CreditCard,Crown,LogOut,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreditCard: () => (/* reexport safe */ _icons_credit_card_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Crown: () => (/* reexport safe */ _icons_crown_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   LogOut: () => (/* reexport safe */ _icons_log_out_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_credit_card_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/credit-card.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _icons_crown_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/crown.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _icons_log_out_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/log-out.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/settings.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/user.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUNyZWRpdENhcmQsQ3Jvd24sTG9nT3V0LFNldHRpbmdzLFVzZXIhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQzhEO0FBQ1g7QUFDRztBQUNHIiwic291cmNlcyI6WyIvVXNlcnMvZGluZXNocy9Eb2N1bWVudHMvRGV2L1Byb2plY3RzL1lUU3VidGl0bGVFeHRyYWN0b3IvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ3JlZGl0Q2FyZCB9IGZyb20gXCIuL2ljb25zL2NyZWRpdC1jYXJkLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ3Jvd24gfSBmcm9tIFwiLi9pY29ucy9jcm93bi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExvZ091dCB9IGZyb20gXCIuL2ljb25zL2xvZy1vdXQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTZXR0aW5ncyB9IGZyb20gXCIuL2ljb25zL3NldHRpbmdzLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlciB9IGZyb20gXCIuL2ljb25zL3VzZXIuanNcIiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=CreditCard,Crown,LogOut,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=CreditCard,Download,HelpCircle,Home,MessageSquare!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=CreditCard,Download,HelpCircle,Home,MessageSquare!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreditCard: () => (/* reexport safe */ _icons_credit_card_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Download: () => (/* reexport safe */ _icons_download_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   HelpCircle: () => (/* reexport safe */ _icons_circle_help_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Home: () => (/* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   MessageSquare: () => (/* reexport safe */ _icons_message_square_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_credit_card_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/credit-card.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _icons_download_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/download.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _icons_circle_help_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/circle-help.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/house.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _icons_message_square_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/message-square.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUNyZWRpdENhcmQsRG93bmxvYWQsSGVscENpcmNsZSxIb21lLE1lc3NhZ2VTcXVhcmUhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQzhEO0FBQ0w7QUFDSztBQUNaIiwic291cmNlcyI6WyIvVXNlcnMvZGluZXNocy9Eb2N1bWVudHMvRGV2L1Byb2plY3RzL1lUU3VidGl0bGVFeHRyYWN0b3IvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ3JlZGl0Q2FyZCB9IGZyb20gXCIuL2ljb25zL2NyZWRpdC1jYXJkLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRG93bmxvYWQgfSBmcm9tIFwiLi9pY29ucy9kb3dubG9hZC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhlbHBDaXJjbGUgfSBmcm9tIFwiLi9pY29ucy9jaXJjbGUtaGVscC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhvbWUgfSBmcm9tIFwiLi9pY29ucy9ob3VzZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1lc3NhZ2VTcXVhcmUgfSBmcm9tIFwiLi9pY29ucy9tZXNzYWdlLXNxdWFyZS5qc1wiIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=CreditCard,Download,HelpCircle,Home,MessageSquare!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FileText,Heart,HelpCircle,Mail,Shield!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FileText,Heart,HelpCircle,Mail,Shield!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FileText: () => (/* reexport safe */ _icons_file_text_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Heart: () => (/* reexport safe */ _icons_heart_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   HelpCircle: () => (/* reexport safe */ _icons_circle_help_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Mail: () => (/* reexport safe */ _icons_mail_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Shield: () => (/* reexport safe */ _icons_shield_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_file_text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/file-text.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _icons_heart_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/heart.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _icons_circle_help_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/circle-help.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _icons_mail_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/mail.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _icons_shield_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/shield.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUZpbGVUZXh0LEhlYXJ0LEhlbHBDaXJjbGUsTWFpbCxTaGllbGQhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQzBEO0FBQ1A7QUFDVztBQUNiIiwic291cmNlcyI6WyIvVXNlcnMvZGluZXNocy9Eb2N1bWVudHMvRGV2L1Byb2plY3RzL1lUU3VidGl0bGVFeHRyYWN0b3IvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRmlsZVRleHQgfSBmcm9tIFwiLi9pY29ucy9maWxlLXRleHQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIZWFydCB9IGZyb20gXCIuL2ljb25zL2hlYXJ0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSGVscENpcmNsZSB9IGZyb20gXCIuL2ljb25zL2NpcmNsZS1oZWxwLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWFpbCB9IGZyb20gXCIuL2ljb25zL21haWwuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTaGllbGQgfSBmcm9tIFwiLi9pY29ucy9zaGllbGQuanNcIiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=FileText,Heart,HelpCircle,Mail,Shield!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=Loader2,LogIn!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************!*\
  !*** __barrel_optimize__?names=Loader2,LogIn!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Loader2: () => (/* reexport safe */ _icons_loader_circle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   LogIn: () => (/* reexport safe */ _icons_log_in_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_loader_circle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/loader-circle.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _icons_log_in_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/log-in.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUxvYWRlcjIsTG9nSW4hPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQzZEIiwic291cmNlcyI6WyIvVXNlcnMvZGluZXNocy9Eb2N1bWVudHMvRGV2L1Byb2plY3RzL1lUU3VidGl0bGVFeHRyYWN0b3IvWVRTdWJ0aXRsZUV4dHJhY3Rvci9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9hZGVyMiB9IGZyb20gXCIuL2ljb25zL2xvYWRlci1jaXJjbGUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBMb2dJbiB9IGZyb20gXCIuL2ljb25zL2xvZy1pbi5qc1wiIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=Loader2,LogIn!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "@radix-ui/react-avatar":
/*!*****************************************!*\
  !*** external "@radix-ui/react-avatar" ***!
  \*****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-avatar");;

/***/ }),

/***/ "@radix-ui/react-dropdown-menu":
/*!************************************************!*\
  !*** external "@radix-ui/react-dropdown-menu" ***!
  \************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-dropdown-menu");;

/***/ }),

/***/ "@radix-ui/react-icons":
/*!****************************************!*\
  !*** external "@radix-ui/react-icons" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@radix-ui/react-icons");

/***/ }),

/***/ "@radix-ui/react-slot":
/*!***************************************!*\
  !*** external "@radix-ui/react-slot" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-slot");;

/***/ }),

/***/ "@stripe/stripe-js":
/*!************************************!*\
  !*** external "@stripe/stripe-js" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@stripe/stripe-js");

/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "framer-motion":
/*!********************************!*\
  !*** external "framer-motion" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("framer-motion");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpricing&preferredRegion=&absolutePagePath=.%2Fpages%2Fpricing.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();