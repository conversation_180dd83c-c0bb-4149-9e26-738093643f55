"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/stripe/checkout";
exports.ids = ["pages/api/stripe/checkout"];
exports.modules = {

/***/ "(api-node)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabaseClient: () => (/* binding */ createServerSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://vsaxiialrhpqdlcpmetn.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzYXhpaWFscmhwcWRsY3BtZXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwOTQ1NzAsImV4cCI6MjA2NTY3MDU3MH0.MDe8-SLF1-73hqcunFsgOEvDYek1ku7Ap_A1UNardA8\" || 0;\n// Only throw error in production runtime (not during build)\nif ((!supabaseUrl || !supabaseAnonKey || supabaseUrl.includes('placeholder')) && \"development\" === 'production' && 0) {}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true,\n        flowType: 'pkce'\n    }\n});\n// Server-side client for API routes (Node.js environment)\nconst createServerSupabaseClient = ()=>{\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!serviceRoleKey) {\n        throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://vsaxiialrhpqdlcpmetn.supabase.co\" || 0, serviceRoleKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL2xpYi9zdXBhYmFzZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXFEO0FBR3JELE1BQU1DLGNBQWNDLDBDQUFvQyxJQUFJLENBQWlDO0FBQzdGLE1BQU1HLGtCQUFrQkgsa05BQXlDLElBQUksQ0FBaUI7QUFFdEYsNERBQTREO0FBQzVELElBQUksQ0FBQyxDQUFDRCxlQUFlLENBQUNJLG1CQUFtQkosWUFBWU0sUUFBUSxDQUFDLGNBQWEsS0FDdkVMLGtCQUF5QixnQkFDekIsQ0FBNkIsRUFBRSxFQUVsQztBQUVNLE1BQU1RLFdBQVdWLG1FQUFZQSxDQUFXQyxhQUFhSSxpQkFBaUI7SUFDM0VNLE1BQU07UUFDSkMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7UUFDaEJDLG9CQUFvQjtRQUNwQkMsVUFBVTtJQUNaO0FBQ0YsR0FBRztBQUVILDBEQUEwRDtBQUNuRCxNQUFNQyw2QkFBNkI7SUFDeEMsTUFBTUMsaUJBQWlCZixRQUFRQyxHQUFHLENBQUNlLHlCQUF5QjtJQUU1RCxJQUFJLENBQUNELGdCQUFnQjtRQUNuQixNQUFNLElBQUlFLE1BQU07SUFDbEI7SUFFQSxPQUFPbkIsbUVBQVlBLENBQ2pCRSwwQ0FBb0MsSUFBSUQsQ0FBV0EsRUFDbkRnQixnQkFDQTtRQUNFTixNQUFNO1lBQ0pDLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7QUFFSixFQUFFIiwic291cmNlcyI6WyIvVXNlcnMvZGluZXNocy9Eb2N1bWVudHMvRGV2L1Byb2plY3RzL1lUU3VidGl0bGVFeHRyYWN0b3IvWVRTdWJ0aXRsZUV4dHJhY3Rvci9saWIvc3VwYWJhc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcbmltcG9ydCB0eXBlIHsgRGF0YWJhc2UgfSBmcm9tICdAL2xpYi90eXBlcy9zdXBhYmFzZSc7XG5cbmNvbnN0IHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIHx8ICdodHRwczovL3BsYWNlaG9sZGVyLnN1cGFiYXNlLmNvJztcbmNvbnN0IHN1cGFiYXNlQW5vbktleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIHx8ICdwbGFjZWhvbGRlci1rZXknO1xuXG4vLyBPbmx5IHRocm93IGVycm9yIGluIHByb2R1Y3Rpb24gcnVudGltZSAobm90IGR1cmluZyBidWlsZClcbmlmICgoIXN1cGFiYXNlVXJsIHx8ICFzdXBhYmFzZUFub25LZXkgfHwgc3VwYWJhc2VVcmwuaW5jbHVkZXMoJ3BsYWNlaG9sZGVyJykpICYmXG4gICAgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJyAmJlxuICAgIHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gIGNvbnNvbGUud2FybignTWlzc2luZyBTdXBhYmFzZSBlbnZpcm9ubWVudCB2YXJpYWJsZXMnKTtcbn1cblxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50PERhdGFiYXNlPihzdXBhYmFzZVVybCwgc3VwYWJhc2VBbm9uS2V5LCB7XG4gIGF1dGg6IHtcbiAgICBhdXRvUmVmcmVzaFRva2VuOiB0cnVlLFxuICAgIHBlcnNpc3RTZXNzaW9uOiB0cnVlLFxuICAgIGRldGVjdFNlc3Npb25JblVybDogdHJ1ZSxcbiAgICBmbG93VHlwZTogJ3BrY2UnXG4gIH1cbn0pO1xuXG4vLyBTZXJ2ZXItc2lkZSBjbGllbnQgZm9yIEFQSSByb3V0ZXMgKE5vZGUuanMgZW52aXJvbm1lbnQpXG5leHBvcnQgY29uc3QgY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQgPSAoKSA9PiB7XG4gIGNvbnN0IHNlcnZpY2VSb2xlS2V5ID0gcHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWTtcbiAgXG4gIGlmICghc2VydmljZVJvbGVLZXkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ01pc3NpbmcgU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSBlbnZpcm9ubWVudCB2YXJpYWJsZScpO1xuICB9XG5cbiAgcmV0dXJuIGNyZWF0ZUNsaWVudDxEYXRhYmFzZT4oXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIHx8IHN1cGFiYXNlVXJsLFxuICAgIHNlcnZpY2VSb2xlS2V5LFxuICAgIHtcbiAgICAgIGF1dGg6IHtcbiAgICAgICAgYXV0b1JlZnJlc2hUb2tlbjogZmFsc2UsXG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiBmYWxzZVxuICAgICAgfVxuICAgIH1cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VBbm9uS2V5IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJpbmNsdWRlcyIsImNvbnNvbGUiLCJ3YXJuIiwic3VwYWJhc2UiLCJhdXRoIiwiYXV0b1JlZnJlc2hUb2tlbiIsInBlcnNpc3RTZXNzaW9uIiwiZGV0ZWN0U2Vzc2lvbkluVXJsIiwiZmxvd1R5cGUiLCJjcmVhdGVTZXJ2ZXJTdXBhYmFzZUNsaWVudCIsInNlcnZpY2VSb2xlS2V5IiwiU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSIsIkVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api-node)/./lib/supabase.ts\n");

/***/ }),

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fstripe%2Fcheckout&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fstripe%2Fcheckout.js&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fstripe%2Fcheckout&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fstripe%2Fcheckout.js&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_stripe_checkout_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/stripe/checkout.js */ \"(api-node)/./pages/api/stripe/checkout.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_api_stripe_checkout_js__WEBPACK_IMPORTED_MODULE_3__]);\n_pages_api_stripe_checkout_js__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_stripe_checkout_js__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_stripe_checkout_js__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/stripe/checkout\",\n        pathname: \"/api/stripe/checkout\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_stripe_checkout_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVNfQVBJJnBhZ2U9JTJGYXBpJTJGc3RyaXBlJTJGY2hlY2tvdXQmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyUyRmFwaSUyRnN0cmlwZSUyRmNoZWNrb3V0LmpzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNFO0FBQzFEO0FBQzJEO0FBQzNEO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQywwREFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMsMERBQVE7QUFDcEM7QUFDTyx3QkFBd0IseUdBQW1CO0FBQ2xEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVELHFDIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlcy9hcGkvc3RyaXBlL2NoZWNrb3V0LmpzXCI7XG4vLyBSZS1leHBvcnQgdGhlIGhhbmRsZXIgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsICdkZWZhdWx0Jyk7XG4vLyBSZS1leHBvcnQgY29uZmlnLlxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCAnY29uZmlnJyk7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9zdHJpcGUvY2hlY2tvdXRcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9zdHJpcGUvY2hlY2tvdXRcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fstripe%2Fcheckout&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fstripe%2Fcheckout.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/stripe/checkout.js":
/*!**************************************!*\
  !*** ./pages/api/stripe/checkout.js ***!
  \**************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"stripe\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/supabase */ \"(api-node)/./lib/supabase.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([stripe__WEBPACK_IMPORTED_MODULE_0__]);\nstripe__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](process.env.STRIPE_SECRET_KEY);\nasync function handler(req, res) {\n    // Set CORS headers\n    res.setHeader('Access-Control-Allow-Origin', '*');\n    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');\n    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');\n    if (req.method === 'OPTIONS') {\n        return res.status(200).end();\n    }\n    if (req.method !== 'POST') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    try {\n        const { priceId, userId, userEmail } = req.body;\n        if (!priceId || !userId || !userEmail) {\n            return res.status(400).json({\n                error: 'Missing required fields'\n            });\n        }\n        // Create or retrieve Stripe customer\n        const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.createServerSupabaseClient)();\n        // Check if user already has a Stripe customer ID\n        const { data: existingUser } = await supabase.from('users').select('stripe_customer_id').eq('id', userId).single();\n        let customerId = existingUser?.stripe_customer_id;\n        if (!customerId) {\n            // Create new Stripe customer\n            const customer = await stripe.customers.create({\n                email: userEmail,\n                metadata: {\n                    supabase_user_id: userId\n                }\n            });\n            customerId = customer.id;\n            // Update user with Stripe customer ID\n            await supabase.from('users').update({\n                stripe_customer_id: customerId\n            }).eq('id', userId);\n        }\n        // Create Stripe checkout session\n        const session = await stripe.checkout.sessions.create({\n            customer: customerId,\n            payment_method_types: [\n                'card'\n            ],\n            line_items: [\n                {\n                    price: priceId,\n                    quantity: 1\n                }\n            ],\n            mode: 'subscription',\n            success_url: `${req.headers.origin || 'http://localhost:5173'}/dashboard?session_id={CHECKOUT_SESSION_ID}`,\n            cancel_url: `${req.headers.origin || 'http://localhost:5173'}/pricing`,\n            metadata: {\n                user_id: userId\n            }\n        });\n        res.status(200).json({\n            sessionId: session.id\n        });\n    } catch (error) {\n        console.error('Error creating checkout session:', error);\n        res.status(500).json({\n            error: 'Failed to create checkout session'\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/stripe/checkout.js\n");

/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "stripe":
/*!*************************!*\
  !*** external "stripe" ***!
  \*************************/
/***/ ((module) => {

module.exports = import("stripe");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fstripe%2Fcheckout&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fstripe%2Fcheckout.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();