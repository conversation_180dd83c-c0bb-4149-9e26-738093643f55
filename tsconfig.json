{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "noEmitOnError": false, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": false, "paths": {"@/*": ["./*"]}, "allowJs": true, "incremental": true, "esModuleInterop": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": [], "references": [{"path": "./tsconfig.node.json"}]}