# 🚀 YouTube Subtitle Extractor - Setup Guide

This guide will walk you through setting up Stripe and Supabase for your YouTube Subtitle Extractor project.

## 📋 Prerequisites

- Node.js 18+ installed
- A Stripe account
- A Supabase account
- Your domain ready (or use localhost for testing)

---

## 🔧 Part 1: Stripe Setup

### Step 1: Create Stripe Account & Get API Keys

1. **Sign up at [stripe.com](https://stripe.com)**
2. **Navigate to Dashboard → Developers → API keys**
3. **Copy these keys:**
   - **Publishable key** (starts with `pk_test_` for test mode)
   - **Secret key** (starts with `sk_test_` for test mode)

### Step 2: Create Products & Pricing

Go to **Products** in your Stripe dashboard and create these 4 products:

#### Product 1: Starter Credit Pack
- **Name**: Starter Credit Pack
- **Price**: $10.00 USD
- **Billing**: One-time payment
- **Description**: 50 credits for YouTube subtitle extraction (valid 6 months)
- **Copy the Price ID** (starts with `price_...`)

#### Product 2: Pro Credit Pack
- **Name**: Pro Credit Pack
- **Price**: $30.00 USD
- **Billing**: One-time payment
- **Description**: 200 credits for YouTube subtitle extraction (valid 6 months)
- **Copy the Price ID**

#### Product 3: Creator Credit Pack
- **Name**: Creator Credit Pack
- **Price**: $75.00 USD
- **Billing**: One-time payment
- **Description**: 600 credits for YouTube subtitle extraction (valid 6 months)
- **Copy the Price ID**

#### Product 4: Enterprise Credit Pack
- **Name**: Enterprise Credit Pack
- **Price**: $150.00 USD
- **Billing**: One-time payment
- **Description**: 1200 credits for YouTube subtitle extraction (valid 6 months)
- **Copy the Price ID**

### Step 3: Set Up Webhook

1. **Go to Developers → Webhooks**
2. **Add endpoint**: `https://yourdomain.com/api/stripe/webhook`
   - For local testing: `https://your-ngrok-url.ngrok.io/api/stripe/webhook`
3. **Select these events:**
   - `checkout.session.completed`
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `customer.created`
4. **Copy the webhook signing secret** (starts with `whsec_...`)

---

## 🗄️ Part 2: Supabase Setup

### Step 1: Create Supabase Project

1. **Sign up at [supabase.com](https://supabase.com)**
2. **Create new project**
   - Choose a name: `youtube-subtitle-extractor`
   - Set a strong database password
   - Choose your region
3. **Wait for project to be ready** (2-3 minutes)

### Step 2: Get Supabase Credentials

1. **Go to Settings → API**
2. **Copy these values:**
   - **Project URL** (e.g., `https://abcdefgh.supabase.co`)
   - **Anon public key** (starts with `eyJ...`)
   - **Service role key** (starts with `eyJ...`) - Keep this secret!

### Step 3: Set Up Database Tables

1. **Go to SQL Editor in Supabase**
2. **Run the SQL from `database-setup.sql`** (copy and paste the entire file)
3. **Click "Run"** to create all tables and functions

### Step 4: Configure Authentication

1. **Go to Authentication → Settings**
2. **Enable Google OAuth** (recommended):
   - Add your Google OAuth credentials
   - Set redirect URLs: `https://yourdomain.com/auth/callback`
3. **Configure email settings** if needed

---

## ⚙️ Part 3: Environment Configuration

### Step 1: Copy Environment File

```bash
cp .env.example .env.local
```

### Step 2: Fill in Your Credentials

Edit `.env.local` with your actual values:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-publishable-key
STRIPE_SECRET_KEY=sk_test_your-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Stripe Price IDs (from Step 2 above)
NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID=price_1234567890
NEXT_PUBLIC_STRIPE_PRO_PRICE_ID=price_0987654321
NEXT_PUBLIC_STRIPE_CREATOR_PRICE_ID=price_1122334455
NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID=price_5544332211

# Application Configuration
NODE_ENV=development
PORT=3001
```

---

## 🚀 Part 4: Testing Your Setup

### Step 1: Install Dependencies

```bash
npm install
```

### Step 2: Start Development Server

```bash
npm run dev
```

### Step 3: Test the Application

1. **Visit** `http://localhost:3001`
2. **Sign up** with Google OAuth
3. **Go to pricing page** and test subscription flow
4. **Check Supabase** to see if user data is created
5. **Check Stripe** to see if customer and subscription are created

---

## 🔍 Troubleshooting

### Common Issues:

1. **"Module not found" errors**: Run `npm install`
2. **Database connection errors**: Check Supabase URL and keys
3. **Stripe webhook errors**: Ensure webhook URL is correct and accessible
4. **Authentication errors**: Check Google OAuth configuration

### Useful Commands:

```bash
# Check if database tables exist
# Run in Supabase SQL Editor:
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

# Reset monthly usage (run monthly)
SELECT public.reset_monthly_usage();
```

---

## 📞 Next Steps

1. **Deploy to production** (Vercel recommended)
2. **Switch to Stripe live mode** when ready
3. **Set up monitoring** for webhook failures
4. **Configure domain** and SSL certificates
5. **Set up monthly usage reset** (cron job or manual)

Your YouTube Subtitle Extractor is now ready! 🎉
