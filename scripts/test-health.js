#!/usr/bin/env node

/**
 * Simple health check script for local development
 * Tests both client and server endpoints
 */

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${color}${message}${colors.reset}`);
}

async function testEndpoint(url, description) {
  try {
    const response = await fetch(url);
    if (response.ok) {
      log(colors.green, `✅ ${description} - OK`);
      return true;
    } else {
      log(colors.red, `❌ ${description} - Status: ${response.status}`);
      return false;
    }
  } catch (error) {
    log(colors.red, `❌ ${description} - Error: ${error.message}`);
    return false;
  }
}

async function runHealthCheck() {
  log(colors.blue, '🧪 Running Health Check...\n');
  
  const tests = [
    {
      url: 'http://localhost:5173',
      description: 'Client (Vite Dev Server)'
    }
  ];
  
  let passed = 0;
  
  for (const test of tests) {
    const result = await testEndpoint(test.url, test.description);
    if (result) passed++;
  }
  
  log(colors.blue, `\n📊 Results: ${passed}/${tests.length} passed`);
  
  if (passed === tests.length) {
    log(colors.green, '🎉 All tests passed!');
  } else {
    log(colors.red, '⚠️  Some tests failed.');
  }
  
  process.exit(passed === tests.length ? 0 : 1);
}

runHealthCheck().catch(console.error);
