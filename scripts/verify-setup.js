#!/usr/bin/env node

/**
 * Setup Verification Script
 * Run this to verify your Stripe and Supabase configuration
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 YouTube Subtitle Extractor - Setup Verification\n');

// Check if .env.local exists
const envPath = path.join(process.cwd(), '.env.local');
if (!fs.existsSync(envPath)) {
  console.error('❌ .env.local file not found!');
  console.log('📝 Please copy .env.example to .env.local and fill in your credentials');
  process.exit(1);
}

// Load environment variables
require('dotenv').config({ path: envPath });

const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
  'STRIPE_SECRET_KEY',
  'STRIPE_WEBHOOK_SECRET',
  'NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID',
  'NEXT_PUBLIC_STRIPE_PRO_PRICE_ID',
  'NEXT_PUBLIC_STRIPE_CREATOR_PRICE_ID',
  'NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID'
];

console.log('📋 Checking environment variables...\n');

let missingVars = [];
let validVars = [];

requiredEnvVars.forEach(varName => {
  const value = process.env[varName];
  if (!value || value.includes('your-') || value.includes('_here')) {
    missingVars.push(varName);
    console.log(`❌ ${varName}: Missing or placeholder value`);
  } else {
    validVars.push(varName);
    console.log(`✅ ${varName}: Configured`);
  }
});

console.log(`\n📊 Summary: ${validVars.length}/${requiredEnvVars.length} variables configured\n`);

if (missingVars.length > 0) {
  console.log('🚨 Missing environment variables:');
  missingVars.forEach(varName => {
    console.log(`   - ${varName}`);
  });
  console.log('\n📖 Please refer to SETUP_GUIDE.md for instructions\n');
}

// Test Supabase connection
console.log('🔗 Testing Supabase connection...');
try {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (supabaseUrl && supabaseKey) {
    console.log(`✅ Supabase URL: ${supabaseUrl}`);
    console.log(`✅ Supabase Key: ${supabaseKey.substring(0, 20)}...`);
  }
} catch (error) {
  console.log('❌ Supabase configuration error:', error.message);
}

// Test Stripe configuration
console.log('\n💳 Testing Stripe configuration...');
try {
  const stripePublishable = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
  const stripeSecret = process.env.STRIPE_SECRET_KEY;
  
  if (stripePublishable && stripeSecret) {
    console.log(`✅ Stripe Publishable: ${stripePublishable.substring(0, 20)}...`);
    console.log(`✅ Stripe Secret: ${stripeSecret.substring(0, 20)}...`);
    
    // Check if keys match environment
    const isTestMode = stripePublishable.startsWith('pk_test_');
    const secretIsTest = stripeSecret.startsWith('sk_test_');
    
    if (isTestMode === secretIsTest) {
      console.log(`✅ Keys are in ${isTestMode ? 'test' : 'live'} mode`);
    } else {
      console.log('⚠️  Warning: Publishable and secret keys are in different modes!');
    }
  }
} catch (error) {
  console.log('❌ Stripe configuration error:', error.message);
}

// Check pricing configuration
console.log('\n💰 Checking pricing configuration...');
const pricingTiers = [
  { name: 'Starter', env: 'NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID', price: '$10' },
  { name: 'Pro', env: 'NEXT_PUBLIC_STRIPE_PRO_PRICE_ID', price: '$30' },
  { name: 'Creator', env: 'NEXT_PUBLIC_STRIPE_CREATOR_PRICE_ID', price: '$75' },
  { name: 'Enterprise', env: 'NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID', price: '$150' }
];

pricingTiers.forEach(tier => {
  const priceId = process.env[tier.env];
  if (priceId && priceId.startsWith('price_')) {
    console.log(`✅ ${tier.name} (${tier.price}): ${priceId}`);
  } else {
    console.log(`❌ ${tier.name} (${tier.price}): Missing or invalid price ID`);
  }
});

console.log('\n🎯 Next Steps:');
if (missingVars.length === 0) {
  console.log('✅ All environment variables are configured!');
  console.log('🚀 You can now run: npm run dev');
  console.log('🌐 Visit: http://localhost:3001');
} else {
  console.log('📝 Complete the missing environment variables');
  console.log('📖 Refer to SETUP_GUIDE.md for detailed instructions');
}

console.log('\n📚 Useful commands:');
console.log('   npm run dev          - Start development server');
console.log('   npm run build        - Build for production');
console.log('   node scripts/verify-setup.js - Run this verification again');

console.log('\n✨ Happy coding! ✨\n');
