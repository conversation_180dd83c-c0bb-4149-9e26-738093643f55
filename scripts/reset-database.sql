-- Reset Database Script
-- Run this in Supabase SQL Editor to clean up and start fresh
-- WARNING: This will delete all existing data!

-- Drop all policies first
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;
DROP POLICY IF EXISTS "Users can view own credit purchases" ON public.credit_purchases;
DROP POLICY IF EXISTS "Service role can manage credit purchases" ON public.credit_purchases;
DROP POLICY IF EXISTS "Users can view own credits" ON public.user_credits;
DROP POLICY IF EXISTS "Service role can manage user credits" ON public.user_credits;
DROP POLICY IF EXISTS "Users can view own usage stats" ON public.usage_stats;
DROP POLICY IF EXISTS "Service role can manage usage stats" ON public.usage_stats;

-- Drop all triggers
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
DROP TRIGGER IF EXISTS update_credit_purchases_updated_at ON public.credit_purchases;
DROP TRIGGER IF EXISTS update_user_credits_updated_at ON public.user_credits;
DROP TRIGGER IF EXISTS update_usage_stats_updated_at ON public.usage_stats;

-- Drop all functions
DROP FUNCTION IF EXISTS public.handle_new_user();
DROP FUNCTION IF EXISTS public.update_updated_at_column();
DROP FUNCTION IF EXISTS public.add_credits_to_user(UUID, INTEGER, TEXT, DECIMAL, TEXT);
DROP FUNCTION IF EXISTS public.consume_credits(UUID, INTEGER);
DROP FUNCTION IF EXISTS public.cleanup_expired_credits();
DROP FUNCTION IF EXISTS public.reset_monthly_usage();

-- Drop all tables (in correct order due to foreign keys)
DROP TABLE IF EXISTS public.usage_stats;
DROP TABLE IF EXISTS public.user_credits;
DROP TABLE IF EXISTS public.credit_purchases;
DROP TABLE IF EXISTS public.subscriptions; -- Old table if it exists
DROP TABLE IF EXISTS public.users;

-- Now you can run the database-setup.sql script fresh!
SELECT 'Database reset complete. You can now run database-setup.sql' as message;
