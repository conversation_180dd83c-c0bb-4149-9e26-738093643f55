-- Migration script to update pricing tiers from 'premium' to 'creator' and 'enterprise'
-- Run this in Supabase SQL Editor if you're upgrading from the old pricing structure

-- First, add the new tier options to the constraint
ALTER TABLE public.subscriptions 
DROP CONSTRAINT IF EXISTS subscriptions_tier_check;

ALTER TABLE public.subscriptions 
ADD CONSTRAINT subscriptions_tier_check 
CHECK (tier IN ('starter', 'pro', 'creator', 'enterprise'));

-- Update any existing 'premium' subscriptions to 'creator' (optional)
-- Uncomment the line below if you have existing premium subscriptions
-- UPDATE public.subscriptions SET tier = 'creator' WHERE tier = 'premium';

-- Verify the changes
SELECT DISTINCT tier FROM public.subscriptions;

-- Show constraint details
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    cc.check_clause
FROM information_schema.table_constraints tc
JOIN information_schema.check_constraints cc 
    ON tc.constraint_name = cc.constraint_name
WHERE tc.table_name = 'subscriptions' 
    AND tc.constraint_type = 'CHECK';
