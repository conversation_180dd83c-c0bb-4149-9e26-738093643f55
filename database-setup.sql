-- YouTube Subtitle Extractor Database Setup
-- Run this SQL in your Supabase SQL editor to set up the required tables
--
-- Note: This script is designed for Supabase and includes Row Level Security (RLS)
-- Make sure to run this in your Supabase SQL Editor

-- Create users table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    stripe_customer_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create credit_purchases table (for one-time credit pack purchases)
CREATE TABLE IF NOT EXISTS public.credit_purchases (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    stripe_payment_intent_id TEXT UNIQUE NOT NULL,
    stripe_customer_id TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('succeeded', 'failed', 'pending', 'canceled')),
    tier TEXT NOT NULL CHECK (tier IN ('starter', 'pro', 'creator', 'enterprise')),
    credits_purchased INTEGER NOT NULL,
    amount_paid DECIMAL(10,2) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL, -- 6 months from purchase
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_credits table (to track current credit balance)
CREATE TABLE IF NOT EXISTS public.user_credits (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    total_credits INTEGER DEFAULT 0,
    used_credits INTEGER DEFAULT 0,
    available_credits INTEGER GENERATED ALWAYS AS (total_credits - used_credits) STORED,
    last_purchase_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create usage_stats table with credit tracking
CREATE TABLE IF NOT EXISTS public.usage_stats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    credits_used_this_month INTEGER DEFAULT 0,
    videos_extracted_this_month INTEGER DEFAULT 0, -- kept for backward compatibility
    last_reset_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_credit_purchases_user_id ON public.credit_purchases(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_purchases_status ON public.credit_purchases(status);
CREATE INDEX IF NOT EXISTS idx_credit_purchases_stripe_payment_intent_id ON public.credit_purchases(stripe_payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_credit_purchases_expires_at ON public.credit_purchases(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_credits_user_id ON public.user_credits(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_stats_user_id ON public.usage_stats(user_id);

-- Enable Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_credits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_stats ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (with IF NOT EXISTS equivalent)
-- Drop existing policies first to avoid conflicts
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;

-- Users can only see and update their own data
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Drop existing policies first to avoid conflicts
DROP POLICY IF EXISTS "Users can view own credit purchases" ON public.credit_purchases;
DROP POLICY IF EXISTS "Service role can manage credit purchases" ON public.credit_purchases;
DROP POLICY IF EXISTS "Users can view own credits" ON public.user_credits;
DROP POLICY IF EXISTS "Service role can manage user credits" ON public.user_credits;
DROP POLICY IF EXISTS "Users can view own usage stats" ON public.usage_stats;
DROP POLICY IF EXISTS "Service role can manage usage stats" ON public.usage_stats;

-- Credit purchases policies
CREATE POLICY "Users can view own credit purchases" ON public.credit_purchases
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage credit purchases" ON public.credit_purchases
    FOR ALL USING (auth.role() = 'service_role');

-- User credits policies
CREATE POLICY "Users can view own credits" ON public.user_credits
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage user credits" ON public.user_credits
    FOR ALL USING (auth.role() = 'service_role');

-- Usage stats policies
CREATE POLICY "Users can view own usage stats" ON public.usage_stats
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage usage stats" ON public.usage_stats
    FOR ALL USING (auth.role() = 'service_role');

-- Create function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name, avatar_url)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_credit_purchases_updated_at BEFORE UPDATE ON public.credit_purchases
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_credits_updated_at BEFORE UPDATE ON public.user_credits
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_usage_stats_updated_at BEFORE UPDATE ON public.usage_stats
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Create function to add credits after successful purchase
CREATE OR REPLACE FUNCTION public.add_credits_to_user(
    p_user_id UUID,
    p_credits INTEGER,
    p_tier TEXT,
    p_amount DECIMAL(10,2),
    p_stripe_payment_intent_id TEXT
)
RETURNS void AS $$
BEGIN
    -- Insert credit purchase record
    INSERT INTO public.credit_purchases (
        user_id,
        stripe_payment_intent_id,
        stripe_customer_id,
        status,
        tier,
        credits_purchased,
        amount_paid,
        expires_at
    ) VALUES (
        p_user_id,
        p_stripe_payment_intent_id,
        (SELECT stripe_customer_id FROM public.users WHERE id = p_user_id),
        'succeeded',
        p_tier,
        p_credits,
        p_amount,
        NOW() + INTERVAL '6 months'
    );

    -- Update user credits
    INSERT INTO public.user_credits (user_id, total_credits, last_purchase_date)
    VALUES (p_user_id, p_credits, NOW())
    ON CONFLICT (user_id)
    DO UPDATE SET
        total_credits = user_credits.total_credits + p_credits,
        last_purchase_date = NOW(),
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to consume credits
CREATE OR REPLACE FUNCTION public.consume_credits(
    p_user_id UUID,
    p_credits INTEGER DEFAULT 1
)
RETURNS BOOLEAN AS $$
DECLARE
    current_available INTEGER;
BEGIN
    -- Get current available credits
    SELECT available_credits INTO current_available
    FROM public.user_credits
    WHERE user_id = p_user_id;

    -- Check if user has enough credits
    IF current_available IS NULL OR current_available < p_credits THEN
        RETURN FALSE;
    END IF;

    -- Consume credits
    UPDATE public.user_credits
    SET used_credits = used_credits + p_credits,
        updated_at = NOW()
    WHERE user_id = p_user_id;

    -- Update usage stats
    INSERT INTO public.usage_stats (user_id, credits_used_this_month)
    VALUES (p_user_id, p_credits)
    ON CONFLICT (user_id)
    DO UPDATE SET
        credits_used_this_month = usage_stats.credits_used_this_month + p_credits,
        updated_at = NOW();

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to clean up expired credits
CREATE OR REPLACE FUNCTION public.cleanup_expired_credits()
RETURNS void AS $$
BEGIN
    -- Mark expired purchases
    UPDATE public.credit_purchases
    SET status = 'expired'
    WHERE expires_at < NOW() AND status = 'succeeded';

    -- You might want to implement logic to remove expired credits from user_credits
    -- This depends on your business logic for handling expired credits
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- You can set up a cron job to run this monthly:
-- SELECT cron.schedule('reset-monthly-usage', '0 0 1 * *', 'SELECT public.reset_monthly_usage();');

-- Migration: Add credits_used_this_month column to existing usage_stats table
-- Run this if you're upgrading from a previous version without credit tracking
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'usage_stats'
        AND column_name = 'credits_used_this_month'
    ) THEN
        ALTER TABLE public.usage_stats
        ADD COLUMN credits_used_this_month INTEGER DEFAULT 0;
    END IF;
END $$;
