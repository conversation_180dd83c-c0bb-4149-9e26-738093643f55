# Stripe Integration Guide

## Overview

This guide covers the complete setup and integration of <PERSON><PERSON> for the YouTube Subtitle Extractor project. The system uses a **credit-based subscription model** where each video extraction costs 2 credits (representing 2 API hits to the proxy server).

## Credit-Based Pricing Model

### Pricing Structure
- **Proxy Server Cost**: $5 for 1GB of data transfer
- **Cost per Video**: 2 credits (2 API hits)
- **User-Friendly Tiers**: Designed for different usage levels

### Subscription Tiers

| Plan | Price | Credits/Month | ~Videos/Month | Features |
|------|-------|---------------|---------------|----------|
| **Starter** | $9 | 100 | ~50 | Basic extraction, VTT/TXT formats |
| **Pro** | $19 | 500 | ~250 | All formats, batch processing, priority support |
| **Premium** | $39 | 1500 | ~750 | High volume, API access, custom integrations |

## Stripe Setup

### 1. Create Stripe Account
1. Go to [stripe.com](https://stripe.com) and create an account
2. Complete business verification
3. Switch to **Live mode** for production

### 2. Create Products and Prices

#### Create Products
1. Go to **Products** in Stripe Dashboard
2. Create three products:
   - **Starter Plan** - 100 credits/month
   - **Pro Plan** - 500 credits/month  
   - **Premium Plan** - 1500 credits/month

#### Create Recurring Prices
For each product, create a monthly recurring price:
1. Click **Add pricing**
2. Set **Billing period**: Monthly
3. Set prices: $9, $19, $39 respectively
4. Copy the **Price ID** (starts with `price_`)

### 3. Environment Variables

Add these to your `.env` file:

```bash
# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...  # or pk_test_ for testing
STRIPE_SECRET_KEY=sk_live_...                   # or sk_test_ for testing
STRIPE_WEBHOOK_SECRET=whsec_...                 # from webhook setup

# Stripe Price IDs (copy from Stripe Dashboard)
NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID=price_1234567890
NEXT_PUBLIC_STRIPE_PRO_PRICE_ID=price_0987654321
NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID=price_1122334455
```

### 4. Webhook Configuration

#### Create Webhook Endpoint
1. Go to **Developers > Webhooks** in Stripe Dashboard
2. Click **Add endpoint**
3. Set **Endpoint URL**: `https://your-domain.com/api/stripe/webhook`
4. Select these events:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

#### Get Webhook Secret
1. Click on your webhook endpoint
2. Copy the **Signing secret** (starts with `whsec_`)
3. Add to `STRIPE_WEBHOOK_SECRET` environment variable

## Database Schema

### Required Tables

The system requires these Supabase tables:

```sql
-- Users table (extends Supabase auth.users)
CREATE TABLE users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT,
  stripe_customer_id TEXT UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscriptions table
CREATE TABLE subscriptions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  stripe_subscription_id TEXT UNIQUE NOT NULL,
  stripe_customer_id TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'incomplete', 'incomplete_expired', 'past_due', 'trialing', 'unpaid')),
  tier TEXT NOT NULL CHECK (tier IN ('starter', 'pro', 'premium')),
  current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
  current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Usage tracking table
CREATE TABLE usage_stats (
  user_id UUID REFERENCES users(id) ON DELETE CASCADE PRIMARY KEY,
  credits_used_this_month INTEGER DEFAULT 0,
  videos_extracted_this_month INTEGER DEFAULT 0, -- backward compatibility
  last_reset_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Testing

### Test Mode Setup
1. Use test API keys (pk_test_, sk_test_)
2. Use test credit card numbers:
   - **Success**: ************** 4242
   - **Decline**: ************** 0002
   - **3D Secure**: ************** 3155

### Test Webhook Locally
1. Install Stripe CLI: `brew install stripe/stripe-cli/stripe`
2. Login: `stripe login`
3. Forward events: `stripe listen --forward-to localhost:3000/api/stripe/webhook`
4. Copy webhook secret from CLI output

### Test Subscription Flow
1. Create test subscription
2. Verify webhook events are received
3. Check database records are created
4. Test credit deduction on video extraction

## Production Deployment

### Vercel Environment Variables
Set these in Vercel Dashboard:
```bash
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID=price_...
NEXT_PUBLIC_STRIPE_PRO_PRICE_ID=price_...
NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID=price_...
```

### Security Checklist
- [ ] Use live API keys in production
- [ ] Verify webhook signatures
- [ ] Enable HTTPS only
- [ ] Set up proper CORS headers
- [ ] Implement rate limiting
- [ ] Monitor webhook delivery

## API Endpoints

### Checkout Session Creation
```javascript
// POST /api/stripe/checkout
{
  "priceId": "price_1234567890",
  "userId": "user-uuid",
  "userEmail": "<EMAIL>"
}
```

### Webhook Handler
```javascript
// POST /api/stripe/webhook
// Handles Stripe webhook events
// Verifies signature and updates database
```

### Subscription Management
```javascript
// GET /api/user/subscription?userId=uuid
// Returns user subscription and usage stats

// DELETE /api/user/subscription
// Cancels user subscription
```

## Error Handling

### Common Issues
1. **Invalid webhook signature**: Check webhook secret
2. **Subscription not found**: Verify Stripe customer ID
3. **Credit deduction fails**: Check usage stats table
4. **Payment fails**: Handle via webhook events

### Monitoring
- Set up Stripe Dashboard alerts
- Monitor webhook delivery success
- Track subscription metrics
- Monitor credit usage patterns

## Support

### Customer Support
- Subscription status queries
- Credit balance inquiries
- Billing issue resolution
- Plan upgrade/downgrade requests

### Admin Tools
- View subscription analytics
- Monitor credit usage
- Handle refunds/adjustments
- Manage failed payments
