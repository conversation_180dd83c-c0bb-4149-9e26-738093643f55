# Source files (only include built .next/)
components/
pages/
lib/

# Development files
node_modules/
.git/
.gitignore
*.md
README.md

# Build tools and configs
next.config.mjs
tsconfig*.json
tailwind.config.js
postcss.config.js
components.json

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel/
vercel.json
.vercelignore
